#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 USAR TIKZ-LLAVA - MODELO SIMPLE HUGGING FACE
===============================================

Script para usar TikZ-llava, un modelo más simple de Hugging Face
que convierte imágenes a código TikZ usando transformers estándar.

Modelo: waleko/TikZ-llava-1.5-7b
Fuente: https://huggingface.co/waleko/TikZ-llava-1.5-7b
"""

import os
import sys
from pathlib import Path

def usar_tikz_llava_con_imagen(ruta_imagen):
    """Usar TikZ-llava para convertir imagen a TikZ."""
    
    print("🎯 USANDO TIKZ-LLAVA - MODELO SIMPLE")
    print(f"📸 Imagen: {ruta_imagen}")
    
    # Verificar si la imagen existe
    if not Path(ruta_imagen).exists():
        print(f"❌ Imagen no encontrada: {ruta_imagen}")
        return None
    
    try:
        print("📦 Importando librerías...")
        from transformers import pipeline
        from PIL import Image
        
        print("🔄 Cargando modelo TikZ-llava...")
        
        # Crear pipeline
        pipe = pipeline("image-to-text", model="waleko/TikZ-llava-1.5-7b")
        
        print("✅ Modelo cargado")
        print("🖼️ Procesando imagen...")
        
        # Cargar imagen
        image = Image.open(ruta_imagen)
        
        # Prompt específico para TikZ
        prompt = "Assistant helps to write down the TikZ code for the user's image. USER: <image>\nWrite down the TikZ code to draw the diagram shown in the image. ASSISTANT: "
        
        print("🎨 Generando código TikZ...")
        
        # Generar código TikZ
        resultado = pipe(image, prompt=prompt)[0]['generated_text']
        
        # Extraer solo el código TikZ del resultado
        codigo_tikz = extraer_codigo_tikz(resultado)
        
        # Guardar el código
        archivo_tikz = "paralelogramos_tikz_llava.tikz"
        with open(archivo_tikz, 'w', encoding='utf-8') as f:
            f.write(codigo_tikz)
        
        print(f"✅ Código TikZ generado: {archivo_tikz}")
        
        # Mostrar el código generado
        print("\n" + "="*60)
        print("📄 CÓDIGO TIKZ GENERADO:")
        print("="*60)
        print(codigo_tikz)
        print("="*60)
        
        return archivo_tikz
        
    except ImportError as e:
        print(f"❌ Error importando librerías: {e}")
        print("💡 Instala con: pip install transformers pillow torch")
        return None
    except Exception as e:
        print(f"❌ Error usando TikZ-llava: {e}")
        return None

def extraer_codigo_tikz(texto_completo):
    """Extraer solo el código TikZ del texto generado."""
    
    # Buscar el inicio del código TikZ
    inicio_tikz = texto_completo.find("\\begin{tikzpicture}")
    fin_tikz = texto_completo.find("\\end{tikzpicture}")
    
    if inicio_tikz != -1 and fin_tikz != -1:
        # Extraer el código TikZ completo
        codigo = texto_completo[inicio_tikz:fin_tikz + len("\\end{tikzpicture}")]
        return codigo
    else:
        # Si no encuentra el formato estándar, devolver todo el texto
        return texto_completo

def usar_tikz_llava_con_url():
    """Ejemplo usando una URL de imagen."""
    
    print("🎯 EJEMPLO CON URL DE IMAGEN")
    
    try:
        from transformers import pipeline
        from PIL import Image
        import requests
        
        pipe = pipeline("image-to-text", model="waleko/TikZ-llava-1.5-7b")
        
        # Imagen de ejemplo del modelo
        url = "https://waleko.github.io/data/image.jpg"
        image = Image.open(requests.get(url, stream=True).raw)
        
        prompt = "Assistant helps to write down the TikZ code for the user's image. USER: <image>\nWrite down the TikZ code to draw the diagram shown in the image. ASSISTANT: "
        
        print("🎨 Generando código TikZ para imagen de ejemplo...")
        resultado = pipe(image, prompt=prompt)[0]['generated_text']
        
        codigo_tikz = extraer_codigo_tikz(resultado)
        
        # Guardar ejemplo
        with open("ejemplo_tikz_llava.tikz", 'w', encoding='utf-8') as f:
            f.write(codigo_tikz)
        
        print("✅ Ejemplo generado: ejemplo_tikz_llava.tikz")
        print("\n" + "="*60)
        print("📄 CÓDIGO TIKZ DE EJEMPLO:")
        print("="*60)
        print(codigo_tikz)
        print("="*60)
        
        return "ejemplo_tikz_llava.tikz"
        
    except Exception as e:
        print(f"❌ Error con ejemplo: {e}")
        return None

def compilar_tikz(archivo_tikz):
    """Compilar código TikZ usando el compilador existente."""
    
    print(f"🔨 Compilando {archivo_tikz}...")
    
    try:
        # Usar el compilador que ya tenemos
        comando = f"python3 ../02-Codigo-Recursivo/compilador_simple.py {archivo_tikz}"
        resultado = os.system(comando)
        
        if resultado == 0:
            print("✅ Compilación exitosa")
            return True
        else:
            print("❌ Error en compilación")
            return False
            
    except Exception as e:
        print(f"❌ Error compilando: {e}")
        return False

def main():
    """Función principal."""
    
    print("🎯 TIKZ-LLAVA - MODELO SIMPLE HUGGING FACE")
    print("=" * 50)
    
    print("📋 OPCIONES:")
    print("1. Procesar imagen local: usar_tikz_llava_con_imagen('tu_imagen.png')")
    print("2. Ejemplo con URL: usar_tikz_llava_con_url()")
    
    print("\n💡 VENTAJAS DE TIKZ-LLAVA:")
    print("✅ Modelo más ligero (7B parámetros)")
    print("✅ Usa transformers estándar")
    print("✅ Fácil instalación")
    print("✅ Especializado en TikZ")
    
    print("\n🔧 DEPENDENCIAS:")
    print("pip install transformers pillow torch requests")
    
    # Verificar dependencias
    try:
        import transformers
        import PIL
        import torch
        print("\n✅ Dependencias disponibles")
        
        # Ejecutar ejemplo
        print("\n🚀 Ejecutando ejemplo...")
        usar_tikz_llava_con_url()
        
    except ImportError as e:
        print(f"\n❌ Faltan dependencias: {e}")
        print("💡 Instala con: pip install transformers pillow torch requests")

if __name__ == "__main__":
    main()
