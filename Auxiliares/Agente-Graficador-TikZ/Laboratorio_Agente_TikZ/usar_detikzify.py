#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 USAR DETIKZIFY - MODELO ESPECIALIZADO HUGGING FACE
====================================================

Script para usar DeTikZify, un modelo especializado de Hugging Face
que convierte imágenes directamente a código TikZ.

Modelo: nllg/detikzify-v2.5-8b
Fuente: https://huggingface.co/nllg/detikzify-v2.5-8b
"""

import os
import sys
from pathlib import Path

def instalar_detikzify():
    """Instalar DeTikZify si no está disponible."""
    try:
        import detikzify
        print("✅ DeTikZify ya está instalado")
        return True
    except ImportError:
        print("📦 Instalando DeTikZify...")
        try:
            os.system("pip install git+https://github.com/potamides/DeTikZify.git")
            import detikzify
            print("✅ DeTikZify instalado correctamente")
            return True
        except Exception as e:
            print(f"❌ Error instalando DeTikZify: {e}")
            return False

def usar_detikzify_con_imagen(ruta_imagen):
    """Usar DeTikZify para convertir imagen a TikZ."""
    
    print("🎯 USANDO DETIKZIFY - MODELO ESPECIALIZADO")
    print(f"📸 Imagen: {ruta_imagen}")
    
    # Verificar si la imagen existe
    if not Path(ruta_imagen).exists():
        print(f"❌ Imagen no encontrada: {ruta_imagen}")
        return None
    
    try:
        # Importar DeTikZify
        from detikzify.model import load
        from detikzify.infer import DetikzifyPipeline
        
        print("🔄 Cargando modelo DeTikZify...")
        
        # Cargar el modelo (puede tomar tiempo la primera vez)
        pipeline = DetikzifyPipeline(*load(
            model_name_or_path="nllg/detikzify-v2.5-8b",
            device_map="auto",
            torch_dtype="bfloat16",
        ))
        
        print("✅ Modelo cargado")
        print("🎨 Generando código TikZ...")
        
        # Generar código TikZ
        fig = pipeline.sample(image=ruta_imagen)
        
        # Guardar el código TikZ
        archivo_tikz = "paralelogramos_detikzify.tikz"
        fig.save(archivo_tikz)
        
        print(f"✅ Código TikZ generado: {archivo_tikz}")
        
        # Intentar compilar si es posible
        if fig.is_rasterizable:
            print("🖼️ Compilando imagen...")
            try:
                imagen_resultado = fig.rasterize()
                archivo_png = "paralelogramos_detikzify.png"
                imagen_resultado.save(archivo_png)
                print(f"✅ Imagen compilada: {archivo_png}")
            except Exception as e:
                print(f"⚠️ Error compilando imagen: {e}")
        else:
            print("⚠️ El código generado no es compilable directamente")
        
        # Mostrar el código generado
        print("\n" + "="*60)
        print("📄 CÓDIGO TIKZ GENERADO:")
        print("="*60)
        
        try:
            with open(archivo_tikz, 'r', encoding='utf-8') as f:
                codigo = f.read()
                print(codigo)
        except:
            print("❌ Error leyendo el archivo generado")
        
        print("="*60)
        
        return archivo_tikz
        
    except ImportError as e:
        print(f"❌ Error importando DeTikZify: {e}")
        print("💡 Intenta instalar con: pip install git+https://github.com/potamides/DeTikZify.git")
        return None
    except Exception as e:
        print(f"❌ Error usando DeTikZify: {e}")
        return None

def usar_detikzify_con_mcts(ruta_imagen, tiempo_minutos=5):
    """Usar DeTikZify con MCTS para mejor calidad."""
    
    print("🎯 USANDO DETIKZIFY CON MCTS (MEJOR CALIDAD)")
    print(f"📸 Imagen: {ruta_imagen}")
    print(f"⏱️ Tiempo: {tiempo_minutos} minutos")
    
    try:
        from operator import itemgetter
        from detikzify.model import load
        from detikzify.infer import DetikzifyPipeline
        
        print("🔄 Cargando modelo...")
        
        pipeline = DetikzifyPipeline(*load(
            model_name_or_path="nllg/detikzify-v2.5-8b",
            device_map="auto",
            torch_dtype="bfloat16",
        ))
        
        print("✅ Modelo cargado")
        print("🧠 Ejecutando MCTS para mejor calidad...")
        
        # Ejecutar MCTS por el tiempo especificado
        timeout_segundos = tiempo_minutos * 60
        figs = set()
        
        for score, fig in pipeline.simulate(image=ruta_imagen, timeout=timeout_segundos):
            figs.add((score, fig))
            print(f"📊 Score: {score:.3f}")
        
        if figs:
            # Obtener el mejor resultado
            best = sorted(figs, key=itemgetter(0))[-1][1]
            
            # Guardar el mejor código
            archivo_tikz = "paralelogramos_detikzify_mcts.tikz"
            best.save(archivo_tikz)
            
            print(f"✅ Mejor código TikZ: {archivo_tikz}")
            
            # Compilar si es posible
            if best.is_rasterizable:
                try:
                    imagen_resultado = best.rasterize()
                    archivo_png = "paralelogramos_detikzify_mcts.png"
                    imagen_resultado.save(archivo_png)
                    print(f"✅ Imagen compilada: {archivo_png}")
                except Exception as e:
                    print(f"⚠️ Error compilando: {e}")
            
            return archivo_tikz
        else:
            print("❌ No se generaron resultados")
            return None
            
    except Exception as e:
        print(f"❌ Error con MCTS: {e}")
        return None

def main():
    """Función principal."""
    
    print("🎯 DETIKZIFY - MODELO ESPECIALIZADO HUGGING FACE")
    print("=" * 50)
    
    # Verificar si tenemos una imagen para procesar
    # Por ahora, como no tenemos la imagen como archivo, vamos a mostrar las instrucciones
    
    print("📋 INSTRUCCIONES DE USO:")
    print("1. Guarda tu imagen como archivo (ej: imagen_original.png)")
    print("2. Ejecuta: usar_detikzify_con_imagen('imagen_original.png')")
    print("3. Para mejor calidad: usar_detikzify_con_mcts('imagen_original.png', 10)")
    
    print("\n💡 VENTAJAS DE DETIKZIFY:")
    print("✅ Modelo especializado en imagen → TikZ")
    print("✅ Entrenado específicamente para figuras científicas")
    print("✅ Usa MCTS para mejorar iterativamente")
    print("✅ Genera código TikZ compilable")
    print("✅ Desarrollado por expertos en el tema")
    
    print("\n🔧 INSTALACIÓN:")
    print("pip install git+https://github.com/potamides/DeTikZify.git")
    
    # Intentar instalar si no está disponible
    if instalar_detikzify():
        print("\n✅ DeTikZify está listo para usar")
        print("📝 Guarda tu imagen y llama a las funciones de arriba")
    else:
        print("\n❌ Problema con la instalación de DeTikZify")

if __name__ == "__main__":
    main()
