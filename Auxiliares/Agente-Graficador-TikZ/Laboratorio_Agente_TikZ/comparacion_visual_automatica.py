#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
👁️ COMPARACIÓN VISUAL AUTOMÁTICA - ANÁLISIS REAL
=================================================

Sistema para comparar automáticamente la imagen original con la generada
y proporcionar métricas objetivas de fidelidad visual.

Autor: Agente TikZ + Augment IA (Comparación Automática)
Fecha: 2025-01-14
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
import json
from datetime import datetime
from pathlib import Path
import tempfile

class ComparacionVisualAutomatica:
    """
    Sistema de comparación visual automática con métricas objetivas.
    """
    
    def __init__(self):
        """Inicializar comparador visual."""
        self.resultados_comparacion = {}
        
    def cargar_y_normalizar_imagen(self, ruta_imagen, tamaño_estandar=(800, 600)):
        """Cargar imagen y normalizarla para comparación."""
        try:
            if not Path(ruta_imagen).exists():
                print(f"❌ Imagen no encontrada: {ruta_imagen}")
                return None
                
            # Cargar imagen
            imagen = Image.open(ruta_imagen)
            
            # Convertir a RGB
            if imagen.mode != 'RGB':
                imagen = imagen.convert('RGB')
            
            # Redimensionar a tamaño estándar
            imagen = imagen.resize(tamaño_estandar, Image.LANCZOS)
            
            # Convertir a escala de grises para análisis
            imagen_gray = imagen.convert('L')
            
            # Convertir a array numpy normalizado
            array_normalizado = np.array(imagen_gray, dtype=np.float64) / 255.0
            
            print(f"✅ Imagen cargada: {ruta_imagen} → {array_normalizado.shape}")
            
            return array_normalizado, imagen
            
        except Exception as e:
            print(f"❌ Error cargando {ruta_imagen}: {e}")
            return None, None
    
    def calcular_metricas_fidelidad(self, img_original, img_generada):
        """Calcular métricas completas de fidelidad visual."""
        
        if img_original is None or img_generada is None:
            return {'error': 'Imágenes no válidas'}
        
        # Asegurar mismo tamaño
        if img_original.shape != img_generada.shape:
            min_h = min(img_original.shape[0], img_generada.shape[0])
            min_w = min(img_original.shape[1], img_generada.shape[1])
            img_original = img_original[:min_h, :min_w]
            img_generada = img_generada[:min_h, :min_w]
        
        metricas = {}
        
        # 1. MSE y PSNR
        mse = np.mean((img_original - img_generada) ** 2)
        if mse > 0:
            psnr = 20 * np.log10(1.0 / np.sqrt(mse))
        else:
            psnr = float('inf')
        
        metricas['mse'] = float(mse)
        metricas['psnr'] = float(psnr)
        metricas['similitud_mse'] = float(1.0 - min(mse, 1.0))
        
        # 2. SSIM básico
        ssim = self._calcular_ssim_basico(img_original, img_generada)
        metricas['ssim'] = ssim
        
        # 3. Correlación cruzada
        correlacion = np.corrcoef(img_original.ravel(), img_generada.ravel())[0, 1]
        if np.isnan(correlacion):
            correlacion = 0.0
        metricas['correlacion_cruzada'] = float(correlacion)
        
        # 4. Análisis de histogramas
        hist_original, _ = np.histogram(img_original.ravel(), bins=256, range=(0, 1))
        hist_generada, _ = np.histogram(img_generada.ravel(), bins=256, range=(0, 1))
        
        # Normalizar histogramas
        hist_original = hist_original.astype(float) / np.sum(hist_original)
        hist_generada = hist_generada.astype(float) / np.sum(hist_generada)
        
        # Correlación de histogramas
        correlacion_hist = np.corrcoef(hist_original, hist_generada)[0, 1]
        if np.isnan(correlacion_hist):
            correlacion_hist = 0.0
        metricas['correlacion_histograma'] = float(correlacion_hist)
        
        # Intersección de histogramas
        interseccion_hist = np.sum(np.minimum(hist_original, hist_generada))
        metricas['interseccion_histograma'] = float(interseccion_hist)
        
        # 5. Análisis de gradientes
        grad_x_orig = np.diff(img_original, axis=1)
        grad_y_orig = np.diff(img_original, axis=0)
        grad_x_gen = np.diff(img_generada, axis=1)
        grad_y_gen = np.diff(img_generada, axis=0)
        
        # Energía de gradientes
        energia_orig = np.sum(grad_x_orig**2) + np.sum(grad_y_orig**2)
        energia_gen = np.sum(grad_x_gen**2) + np.sum(grad_y_gen**2)
        
        if max(energia_orig, energia_gen) > 0:
            similitud_gradientes = 1.0 - abs(energia_orig - energia_gen) / max(energia_orig, energia_gen)
        else:
            similitud_gradientes = 1.0
        
        metricas['energia_gradientes_original'] = float(energia_orig)
        metricas['energia_gradientes_generada'] = float(energia_gen)
        metricas['similitud_gradientes'] = float(similitud_gradientes)
        
        # 6. Fidelidad total ponderada
        pesos = {
            'similitud_mse': 0.25,
            'ssim': 0.30,
            'correlacion_cruzada': 0.20,
            'correlacion_histograma': 0.15,
            'similitud_gradientes': 0.10
        }
        
        fidelidad_total = 0.0
        for metrica, peso in pesos.items():
            valor = metricas.get(metrica, 0.0)
            fidelidad_total += valor * peso
        
        metricas['fidelidad_total'] = float(max(0.0, min(1.0, fidelidad_total)))
        
        return metricas
    
    def _calcular_ssim_basico(self, img1, img2):
        """Calcular SSIM básico."""
        # Parámetros SSIM
        K1, K2 = 0.01, 0.03
        L = 1.0
        C1 = (K1 * L) ** 2
        C2 = (K2 * L) ** 2
        
        # Medias
        mu1 = np.mean(img1)
        mu2 = np.mean(img2)
        
        mu1_sq = mu1 ** 2
        mu2_sq = mu2 ** 2
        mu1_mu2 = mu1 * mu2
        
        # Varianzas y covarianza
        sigma1_sq = np.var(img1)
        sigma2_sq = np.var(img2)
        sigma12 = np.mean((img1 - mu1) * (img2 - mu2))
        
        # SSIM
        numerador = (2 * mu1_mu2 + C1) * (2 * sigma12 + C2)
        denominador = (mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2)
        
        if denominador > 0:
            ssim = numerador / denominador
        else:
            ssim = 0.0
        
        return float(ssim)
    
    def generar_imagen_comparacion(self, img_original_pil, img_generada_pil, metricas):
        """Generar imagen visual de comparación."""
        
        # Crear imagen de comparación lado a lado
        ancho_total = img_original_pil.width + img_generada_pil.width + 50
        alto_total = max(img_original_pil.height, img_generada_pil.height) + 100
        
        imagen_comparacion = Image.new('RGB', (ancho_total, alto_total), 'white')
        
        # Pegar imágenes
        imagen_comparacion.paste(img_original_pil, (10, 50))
        imagen_comparacion.paste(img_generada_pil, (img_original_pil.width + 30, 50))
        
        # Añadir texto con métricas
        draw = ImageDraw.Draw(imagen_comparacion)
        
        try:
            # Intentar cargar fuente
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        # Títulos
        draw.text((10, 10), "IMAGEN ORIGINAL", fill='black', font=font)
        draw.text((img_original_pil.width + 30, 10), "IMAGEN GENERADA", fill='black', font=font)
        
        # Métricas principales
        y_pos = img_original_pil.height + 70
        fidelidad = metricas.get('fidelidad_total', 0.0)
        ssim = metricas.get('ssim', 0.0)
        mse = metricas.get('mse', 1.0)
        
        draw.text((10, y_pos), f"FIDELIDAD TOTAL: {fidelidad:.2%}", fill='green' if fidelidad >= 0.9 else 'orange' if fidelidad >= 0.7 else 'red', font=font)
        draw.text((10, y_pos + 20), f"SSIM: {ssim:.3f}", fill='black', font=font)
        draw.text((300, y_pos), f"MSE: {mse:.4f}", fill='black', font=font)
        draw.text((300, y_pos + 20), f"PSNR: {metricas.get('psnr', 0.0):.1f} dB", fill='black', font=font)
        
        return imagen_comparacion
    
    def realizar_comparacion_completa(self, ruta_original, ruta_generada):
        """Realizar comparación visual completa."""
        
        print("👁️ INICIANDO COMPARACIÓN VISUAL AUTOMÁTICA...")
        print(f"📸 Original: {ruta_original}")
        print(f"🖼️ Generada: {ruta_generada}")
        
        # Cargar imágenes
        img_orig_array, img_orig_pil = self.cargar_y_normalizar_imagen(ruta_original)
        img_gen_array, img_gen_pil = self.cargar_y_normalizar_imagen(ruta_generada)
        
        if img_orig_array is None or img_gen_array is None:
            print("❌ Error: No se pudieron cargar las imágenes")
            return None
        
        # Calcular métricas
        print("🔬 Calculando métricas de fidelidad...")
        metricas = self.calcular_metricas_fidelidad(img_orig_array, img_gen_array)
        
        # Generar imagen de comparación
        print("🖼️ Generando imagen de comparación...")
        imagen_comparacion = self.generar_imagen_comparacion(img_orig_pil, img_gen_pil, metricas)
        
        # Guardar imagen de comparación
        archivo_comparacion = "comparacion_visual_automatica.png"
        imagen_comparacion.save(archivo_comparacion)
        print(f"💾 Imagen de comparación guardada: {archivo_comparacion}")
        
        # Evaluar resultado
        fidelidad = metricas.get('fidelidad_total', 0.0)
        
        if fidelidad >= 0.98:
            estado = "EXCELENTE"
            emoji = "🎉"
        elif fidelidad >= 0.90:
            estado = "MUY BUENO"
            emoji = "✅"
        elif fidelidad >= 0.75:
            estado = "BUENO"
            emoji = "👍"
        else:
            estado = "REQUIERE MEJORA"
            emoji = "⚠️"
        
        # Compilar resultado
        resultado = {
            'timestamp': datetime.now().isoformat(),
            'imagenes': {
                'original': ruta_original,
                'generada': ruta_generada
            },
            'metricas_detalladas': metricas,
            'evaluacion': {
                'fidelidad_total': fidelidad,
                'estado': estado,
                'objetivo_98_alcanzado': fidelidad >= 0.98,
                'emoji': emoji
            },
            'archivo_comparacion': archivo_comparacion
        }
        
        # Mostrar resultados
        print("\n" + "="*60)
        print("📊 RESULTADOS DE COMPARACIÓN VISUAL")
        print("="*60)
        print(f"{emoji} ESTADO: {estado}")
        print(f"🎯 FIDELIDAD TOTAL: {fidelidad:.2%}")
        print(f"📈 SSIM: {metricas.get('ssim', 0.0):.3f}")
        print(f"📉 MSE: {metricas.get('mse', 1.0):.4f}")
        print(f"📊 PSNR: {metricas.get('psnr', 0.0):.1f} dB")
        print(f"🔗 Correlación: {metricas.get('correlacion_cruzada', 0.0):.3f}")
        print(f"📈 Histograma: {metricas.get('correlacion_histograma', 0.0):.3f}")
        print(f"🌊 Gradientes: {metricas.get('similitud_gradientes', 0.0):.3f}")
        print("="*60)
        
        if fidelidad >= 0.98:
            print("🎉 ¡OBJETIVO 98% ALCANZADO!")
        else:
            print(f"🎯 Faltan {0.98 - fidelidad:.2%} para alcanzar 98%")
        
        print(f"🖼️ Ver comparación visual: {archivo_comparacion}")
        
        # Guardar resultado completo
        with open('resultado_comparacion_automatica.json', 'w', encoding='utf-8') as f:
            json.dump(resultado, f, indent=2, ensure_ascii=False)
        
        return resultado

def main():
    """Ejecutar comparación visual automática."""
    
    # Crear comparador
    comparador = ComparacionVisualAutomatica()
    
    # Buscar imágenes disponibles
    imagen_generada = "paralelogramos_ULTRA_PRECISO_V2.png"
    
    # Como no tenemos la imagen original como archivo, vamos a simular
    # una comparación usando la imagen generada contra sí misma para
    # verificar que el sistema funciona
    
    if Path(imagen_generada).exists():
        print(f"🔍 Realizando auto-verificación del sistema con: {imagen_generada}")
        
        # Auto-comparación para verificar sistema
        resultado = comparador.realizar_comparacion_completa(imagen_generada, imagen_generada)
        
        if resultado:
            print("\n✅ Sistema de comparación automática funcionando correctamente")
            print("📝 Para comparación real, proporcionar imagen original como archivo")
            
            return resultado
    else:
        print(f"❌ Imagen no encontrada: {imagen_generada}")
        return None

if __name__ == "__main__":
    main()
