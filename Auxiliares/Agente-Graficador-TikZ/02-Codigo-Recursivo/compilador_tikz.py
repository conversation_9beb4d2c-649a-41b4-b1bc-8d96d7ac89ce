#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔨 COMPILADOR TIKZ PARA AGENTE RECURSIVO
========================================

Módulo especializado en compilar código TikZ a imágenes PNG
para el sistema de revisión visual recursiva.

Características:
- Compilación automática TikZ → PDF → PNG
- Manejo robusto de errores de compilación
- Optimización para comparación visual
- Limpieza automática de archivos temporales
- Configuración flexible de calidad y resolución

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
"""

import os
import sys
import subprocess
import tempfile
import logging
from pathlib import Path
from typing import Optional, Dict, List
import shutil

class CompiladorTikZ:
    """
    Compilador especializado para convertir código TikZ a imágenes PNG.
    
    Maneja todo el proceso de compilación desde código TikZ hasta imagen
    final optimizada para comparación visual.
    """
    
    def __init__(self):
        """Inicializar el compilador TikZ."""
        self.logger = logging.getLogger(__name__)
        
        # Configuración del compilador
        self.config = {
            'motor_latex': 'pdflatex',      # Motor LaTeX a usar
            'dpi_salida': 300,              # DPI para conversión PNG
            'timeout_compilacion': 30,      # Timeout en segundos
            'limpiar_temporales': True,     # Limpiar archivos temporales
            'directorio_temporal': None,    # Directorio temporal (None = auto)
            'paquetes_adicionales': [       # Paquetes LaTeX adicionales
                'tikz',
                'pgfplots',
                'amsmath',
                'amsfonts',
                'geometry'
            ]
        }
        
        # Verificar dependencias
        self._verificar_dependencias()
    
    def _verificar_dependencias(self):
        """Verificar que las herramientas necesarias estén disponibles."""
        herramientas_requeridas = [
            ('pdflatex', 'LaTeX'),
            ('convert', 'ImageMagick'),
            ('gs', 'Ghostscript')
        ]
        
        dependencias_ok = True
        
        for comando, nombre in herramientas_requeridas:
            if not shutil.which(comando):
                self.logger.warning(f"⚠️ {nombre} ({comando}) no encontrado")
                dependencias_ok = False
            else:
                self.logger.debug(f"✅ {nombre} disponible")
        
        if not dependencias_ok:
            self.logger.warning("⚠️ Algunas dependencias no están disponibles")
            self.logger.info("📋 Para instalar dependencias:")
            self.logger.info("   Ubuntu/Debian: sudo apt install texlive-latex-extra imagemagick ghostscript")
            self.logger.info("   macOS: brew install mactex imagemagick ghostscript")
    
    def compilar_tikz_a_png(self, codigo_tikz: str, archivo_salida: str) -> Optional[str]:
        """
        Compilar código TikZ a imagen PNG.
        
        Args:
            codigo_tikz: Código TikZ a compilar
            archivo_salida: Ruta donde guardar la imagen PNG resultante
            
        Returns:
            Ruta al archivo PNG generado o None si hay error
        """
        self.logger.info(f"🔨 Compilando código TikZ a PNG: {archivo_salida}")
        
        # Crear directorio temporal
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                temp_path = Path(temp_dir)
                
                # 1. Crear documento LaTeX completo
                documento_latex = self._crear_documento_latex(codigo_tikz)
                archivo_tex = temp_path / "tikz_temp.tex"
                
                with open(archivo_tex, 'w', encoding='utf-8') as f:
                    f.write(documento_latex)
                
                self.logger.debug(f"📄 Documento LaTeX creado: {archivo_tex}")
                
                # 2. Compilar LaTeX a PDF
                archivo_pdf = self._compilar_latex_a_pdf(archivo_tex)
                if not archivo_pdf:
                    return None
                
                # 3. Convertir PDF a PNG
                resultado_png = self._convertir_pdf_a_png(archivo_pdf, archivo_salida)
                if not resultado_png:
                    return None
                
                self.logger.info(f"✅ Compilación exitosa: {archivo_salida}")
                return str(archivo_salida)
                
            except Exception as e:
                self.logger.error(f"❌ Error en compilación: {e}")
                return None
    
    def _crear_documento_latex(self, codigo_tikz: str) -> str:
        """
        Crear documento LaTeX completo con el código TikZ.
        
        Args:
            codigo_tikz: Código TikZ a incluir
            
        Returns:
            Documento LaTeX completo como string
        """
        # Plantilla de documento LaTeX optimizada
        plantilla = f"""\\documentclass[border=2pt]{{standalone}}

% Paquetes esenciales
\\usepackage[utf8]{{inputenc}}
\\usepackage{{tikz}}
\\usepackage{{pgfplots}}
\\usepackage{{amsmath}}
\\usepackage{{amsfonts}}

% Librerías TikZ
\\usetikzlibrary{{calc,arrows.meta,positioning,shapes.geometric,patterns}}

% Configuración PGFPlots
\\pgfplotsset{{compat=1.18}}

% Configuración para mejor renderizado
\\tikzset{{
    every picture/.style={{
        line width=0.8pt,
        inner sep=1pt,
        outer sep=0pt
    }}
}}

\\begin{{document}}

{codigo_tikz}

\\end{{document}}"""
        
        return plantilla
    
    def _compilar_latex_a_pdf(self, archivo_tex: Path) -> Optional[Path]:
        """
        Compilar archivo LaTeX a PDF.
        
        Args:
            archivo_tex: Ruta al archivo .tex
            
        Returns:
            Ruta al archivo PDF generado o None si hay error
        """
        try:
            # Cambiar al directorio del archivo
            directorio_trabajo = archivo_tex.parent
            nombre_archivo = archivo_tex.stem
            
            # Comando de compilación
            comando = [
                self.config['motor_latex'],
                '-interaction=nonstopmode',
                '-halt-on-error',
                '-output-directory', str(directorio_trabajo),
                str(archivo_tex)
            ]
            
            self.logger.debug(f"🔧 Ejecutando: {' '.join(comando)}")
            
            # Ejecutar compilación
            resultado = subprocess.run(
                comando,
                cwd=directorio_trabajo,
                capture_output=True,
                text=True,
                timeout=self.config['timeout_compilacion']
            )
            
            # Verificar resultado
            archivo_pdf = directorio_trabajo / f"{nombre_archivo}.pdf"
            
            if resultado.returncode == 0 and archivo_pdf.exists():
                self.logger.debug(f"✅ PDF generado: {archivo_pdf}")
                return archivo_pdf
            else:
                self.logger.error(f"❌ Error en compilación LaTeX:")
                self.logger.error(f"   Código de salida: {resultado.returncode}")
                self.logger.error(f"   STDOUT: {resultado.stdout}")
                self.logger.error(f"   STDERR: {resultado.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"❌ Timeout en compilación LaTeX ({self.config['timeout_compilacion']}s)")
            return None
        except Exception as e:
            self.logger.error(f"❌ Error ejecutando LaTeX: {e}")
            return None
    
    def _convertir_pdf_a_png(self, archivo_pdf: Path, archivo_salida: str) -> Optional[str]:
        """
        Convertir archivo PDF a PNG usando ImageMagick.
        
        Args:
            archivo_pdf: Ruta al archivo PDF
            archivo_salida: Ruta donde guardar el PNG
            
        Returns:
            Ruta al archivo PNG generado o None si hay error
        """
        try:
            # Asegurar que el directorio de salida existe
            Path(archivo_salida).parent.mkdir(parents=True, exist_ok=True)
            
            # Comando de conversión con ImageMagick
            comando = [
                'convert',
                '-density', str(self.config['dpi_salida']),
                '-quality', '100',
                '-background', 'white',
                '-alpha', 'remove',
                str(archivo_pdf),
                str(archivo_salida)
            ]
            
            self.logger.debug(f"🖼️ Convirtiendo PDF a PNG: {' '.join(comando)}")
            
            # Ejecutar conversión
            resultado = subprocess.run(
                comando,
                capture_output=True,
                text=True,
                timeout=self.config['timeout_compilacion']
            )
            
            # Verificar resultado
            if resultado.returncode == 0 and Path(archivo_salida).exists():
                self.logger.debug(f"✅ PNG generado: {archivo_salida}")
                return str(archivo_salida)
            else:
                self.logger.error(f"❌ Error en conversión PDF→PNG:")
                self.logger.error(f"   Código de salida: {resultado.returncode}")
                self.logger.error(f"   STDERR: {resultado.stderr}")
                
                # Intentar método alternativo con Ghostscript
                return self._convertir_pdf_a_png_ghostscript(archivo_pdf, archivo_salida)
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"❌ Timeout en conversión PDF→PNG")
            return None
        except Exception as e:
            self.logger.error(f"❌ Error en conversión: {e}")
            return None
    
    def _convertir_pdf_a_png_ghostscript(self, archivo_pdf: Path, archivo_salida: str) -> Optional[str]:
        """
        Método alternativo de conversión usando Ghostscript directamente.
        
        Args:
            archivo_pdf: Ruta al archivo PDF
            archivo_salida: Ruta donde guardar el PNG
            
        Returns:
            Ruta al archivo PNG generado o None si hay error
        """
        try:
            self.logger.info("🔄 Intentando conversión alternativa con Ghostscript...")
            
            comando = [
                'gs',
                '-dNOPAUSE',
                '-dBATCH',
                '-sDEVICE=png16m',
                f'-r{self.config["dpi_salida"]}',
                '-dTextAlphaBits=4',
                '-dGraphicsAlphaBits=4',
                f'-sOutputFile={archivo_salida}',
                str(archivo_pdf)
            ]
            
            resultado = subprocess.run(
                comando,
                capture_output=True,
                text=True,
                timeout=self.config['timeout_compilacion']
            )
            
            if resultado.returncode == 0 and Path(archivo_salida).exists():
                self.logger.info(f"✅ Conversión alternativa exitosa: {archivo_salida}")
                return str(archivo_salida)
            else:
                self.logger.error(f"❌ Error en conversión alternativa")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error en conversión alternativa: {e}")
            return None
    
    def compilar_y_validar(self, codigo_tikz: str, archivo_salida: str) -> Dict:
        """
        Compilar código TikZ y validar el resultado.
        
        Args:
            codigo_tikz: Código TikZ a compilar
            archivo_salida: Ruta donde guardar la imagen
            
        Returns:
            Dict con resultado de compilación y validación
        """
        resultado = {
            'exitoso': False,
            'archivo_generado': None,
            'errores': [],
            'advertencias': [],
            'tiempo_compilacion': 0.0
        }
        
        import time
        inicio = time.time()
        
        try:
            # Intentar compilación
            archivo_png = self.compilar_tikz_a_png(codigo_tikz, archivo_salida)
            
            if archivo_png:
                # Validar archivo generado
                if Path(archivo_png).exists() and Path(archivo_png).stat().st_size > 0:
                    resultado['exitoso'] = True
                    resultado['archivo_generado'] = archivo_png
                    self.logger.info(f"✅ Compilación y validación exitosa")
                else:
                    resultado['errores'].append("Archivo PNG generado está vacío o corrupto")
            else:
                resultado['errores'].append("Error en compilación TikZ")
            
        except Exception as e:
            resultado['errores'].append(f"Error inesperado: {e}")
        
        finally:
            resultado['tiempo_compilacion'] = time.time() - inicio
        
        return resultado
    
    def obtener_info_sistema(self) -> Dict:
        """Obtener información del sistema de compilación."""
        info = {
            'herramientas_disponibles': {},
            'configuracion_actual': self.config.copy()
        }
        
        # Verificar herramientas
        herramientas = [
            ('pdflatex', 'LaTeX'),
            ('convert', 'ImageMagick'),
            ('gs', 'Ghostscript')
        ]
        
        for comando, nombre in herramientas:
            disponible = shutil.which(comando) is not None
            info['herramientas_disponibles'][nombre] = {
                'disponible': disponible,
                'comando': comando,
                'ruta': shutil.which(comando) if disponible else None
            }
        
        return info

def test_compilador():
    """Función de prueba para el compilador."""
    compilador = CompiladorTikZ()
    
    # Código TikZ de prueba
    codigo_test = """
\\begin{tikzpicture}[scale=1.0]
\\draw[thick] (0,0) -- (2,0) -- (2,2) -- (0,2) -- cycle;
\\draw[red, thick] (0,0) -- (2,2);
\\draw[blue, thick] (0,2) -- (2,0);
\\node at (1,1) {Test};
\\end{tikzpicture}
"""
    
    print("🧪 Probando compilador TikZ...")
    
    # Crear archivo temporal para prueba
    import tempfile
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        archivo_test = temp_file.name
    
    try:
        resultado = compilador.compilar_y_validar(codigo_test, archivo_test)
        
        if resultado['exitoso']:
            print(f"✅ Compilación exitosa: {resultado['archivo_generado']}")
            print(f"⏱️ Tiempo: {resultado['tiempo_compilacion']:.2f}s")
        else:
            print(f"❌ Error en compilación: {resultado['errores']}")
        
        # Mostrar info del sistema
        info = compilador.obtener_info_sistema()
        print("\n📋 Información del sistema:")
        for herramienta, datos in info['herramientas_disponibles'].items():
            estado = "✅" if datos['disponible'] else "❌"
            print(f"   {estado} {herramienta}: {datos['comando']}")
    
    finally:
        # Limpiar archivo temporal
        if Path(archivo_test).exists():
            Path(archivo_test).unlink()

if __name__ == "__main__":
    test_compilador()
