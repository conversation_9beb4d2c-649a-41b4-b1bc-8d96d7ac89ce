#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 INSTALADOR DE DEPENDENCIAS GLOBALES PARA AGENTE TIKZ
=======================================================

Script para instalar todas las dependencias necesarias de manera GLOBAL
en el sistema, sin usar entornos virtuales.

DEPENDENCIAS REQUERIDAS:
- opencv-python (cv2)
- numpy
- pillow (PIL)
- scikit-image (skimage)
- scipy
- matplotlib

INSTALACIÓN GLOBAL:
- Usa pip del sistema global
- NO crea entornos virtuales
- Instala para todos los usuarios
- Compatible con Augment IA en VSCode

Uso: python3 instalar_dependencias_globales.py

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
Autorización: sudo ProShectos
"""

import subprocess
import sys
import os
from pathlib import Path

class InstaladorDependenciasGlobales:
    """
    Instalador de dependencias globales para el Agente TikZ.
    
    Instala todas las librerías necesarias de manera global
    sin usar entornos virtuales.
    """
    
    def __init__(self):
        """Inicializar instalador."""
        self.dependencias_requeridas = [
            # Librerías básicas
            ('opencv-python', 'cv2', 'Procesamiento de imágenes con OpenCV'),
            ('numpy', 'numpy', 'Computación numérica'),
            ('pillow', 'PIL', 'Manipulación de imágenes'),
            
            # Librerías avanzadas para análisis visual
            ('scikit-image', 'skimage', 'Análisis avanzado de imágenes'),
            ('scipy', 'scipy', 'Computación científica'),
            ('matplotlib', 'matplotlib', 'Visualización y gráficos'),
            
            # Librerías adicionales útiles
            ('requests', 'requests', 'Comunicación HTTP'),
            ('psutil', 'psutil', 'Información del sistema')
        ]
        
        self.dependencias_opcionales = [
            ('tensorflow', 'tensorflow', 'Machine Learning (opcional)'),
            ('torch', 'torch', 'PyTorch para ML (opcional)'),
            ('pandas', 'pandas', 'Análisis de datos (opcional)')
        ]
    
    def verificar_pip_global(self):
        """Verificar que pip esté disponible globalmente."""
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ pip disponible: {result.stdout.strip()}")
                return True
            else:
                print("❌ pip no disponible")
                return False
        except Exception as e:
            print(f"❌ Error verificando pip: {e}")
            return False
    
    def verificar_dependencia_instalada(self, nombre_import):
        """Verificar si una dependencia ya está instalada."""
        try:
            __import__(nombre_import)
            return True
        except ImportError:
            return False
    
    def instalar_dependencia(self, nombre_paquete, nombre_import, descripcion):
        """Instalar una dependencia específica de manera global."""
        print(f"\n📦 Instalando {nombre_paquete} ({descripcion})...")
        
        # Verificar si ya está instalada
        if self.verificar_dependencia_instalada(nombre_import):
            print(f"✅ {nombre_paquete} ya está instalado globalmente")
            return True
        
        try:
            # Instalar globalmente
            comando = [sys.executable, '-m', 'pip', 'install', '--upgrade', nombre_paquete]
            
            print(f"🔧 Ejecutando: {' '.join(comando)}")
            
            result = subprocess.run(comando, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {nombre_paquete} instalado exitosamente")
                
                # Verificar instalación
                if self.verificar_dependencia_instalada(nombre_import):
                    print(f"✅ {nombre_paquete} verificado y funcionando")
                    return True
                else:
                    print(f"⚠️ {nombre_paquete} instalado pero no se puede importar")
                    return False
            else:
                print(f"❌ Error instalando {nombre_paquete}:")
                print(f"   STDOUT: {result.stdout}")
                print(f"   STDERR: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Excepción instalando {nombre_paquete}: {e}")
            return False
    
    def instalar_todas_dependencias(self, incluir_opcionales=False):
        """Instalar todas las dependencias requeridas."""
        print("🚀 INSTALADOR DE DEPENDENCIAS GLOBALES - AGENTE TIKZ")
        print("=" * 60)
        
        # Verificar pip
        if not self.verificar_pip_global():
            print("❌ pip no está disponible. Instala Python y pip primero.")
            return False
        
        # Instalar dependencias requeridas
        print("\n📋 INSTALANDO DEPENDENCIAS REQUERIDAS:")
        dependencias_exitosas = 0
        dependencias_fallidas = []
        
        for nombre_paquete, nombre_import, descripcion in self.dependencias_requeridas:
            if self.instalar_dependencia(nombre_paquete, nombre_import, descripcion):
                dependencias_exitosas += 1
            else:
                dependencias_fallidas.append(nombre_paquete)
        
        # Instalar dependencias opcionales si se solicita
        if incluir_opcionales:
            print("\n📋 INSTALANDO DEPENDENCIAS OPCIONALES:")
            for nombre_paquete, nombre_import, descripcion in self.dependencias_opcionales:
                if self.instalar_dependencia(nombre_paquete, nombre_import, descripcion):
                    dependencias_exitosas += 1
                else:
                    dependencias_fallidas.append(nombre_paquete)
        
        # Resumen final
        print("\n" + "=" * 60)
        print("📊 RESUMEN DE INSTALACIÓN:")
        print(f"✅ Dependencias instaladas exitosamente: {dependencias_exitosas}")
        
        if dependencias_fallidas:
            print(f"❌ Dependencias que fallaron: {len(dependencias_fallidas)}")
            for dep in dependencias_fallidas:
                print(f"   - {dep}")
        
        # Verificación final
        print("\n🔍 VERIFICACIÓN FINAL:")
        self.verificar_instalacion_completa()
        
        return len(dependencias_fallidas) == 0
    
    def verificar_instalacion_completa(self):
        """Verificar que todas las dependencias estén correctamente instaladas."""
        print("Verificando instalación completa...")
        
        verificaciones = [
            ('cv2', 'OpenCV'),
            ('numpy', 'NumPy'),
            ('PIL', 'Pillow'),
            ('skimage', 'scikit-image'),
            ('scipy', 'SciPy'),
            ('matplotlib', 'Matplotlib')
        ]
        
        todas_ok = True
        
        for modulo, nombre in verificaciones:
            try:
                __import__(modulo)
                print(f"✅ {nombre} funcionando correctamente")
            except ImportError:
                print(f"❌ {nombre} no disponible")
                todas_ok = False
        
        if todas_ok:
            print("\n🎉 ¡TODAS LAS DEPENDENCIAS INSTALADAS CORRECTAMENTE!")
            print("🚀 El Agente TikZ está listo para funcionar")
        else:
            print("\n⚠️ Algunas dependencias no están disponibles")
            print("💡 Intenta instalar manualmente las que fallaron")
        
        return todas_ok
    
    def generar_script_instalacion_sistema(self):
        """Generar script de instalación para diferentes sistemas operativos."""
        
        # Script para Ubuntu/Debian
        script_ubuntu = """#!/bin/bash
# Script de instalación para Ubuntu/Debian
echo "🐧 Instalando dependencias para Agente TikZ en Ubuntu/Debian..."

# Actualizar sistema
sudo apt update

# Instalar Python y pip si no están instalados
sudo apt install -y python3 python3-pip

# Instalar dependencias del sistema para OpenCV
sudo apt install -y python3-opencv

# Instalar librerías Python globalmente
pip3 install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

echo "✅ Instalación completada para Ubuntu/Debian"
"""
        
        # Script para macOS
        script_macos = """#!/bin/bash
# Script de instalación para macOS
echo "🍎 Instalando dependencias para Agente TikZ en macOS..."

# Verificar que Homebrew esté instalado
if ! command -v brew &> /dev/null; then
    echo "❌ Homebrew no encontrado. Instala desde https://brew.sh/"
    exit 1
fi

# Instalar Python si no está instalado
brew install python

# Instalar librerías Python globalmente
pip3 install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

echo "✅ Instalación completada para macOS"
"""
        
        # Script para Windows
        script_windows = """@echo off
REM Script de instalación para Windows
echo 🪟 Instalando dependencias para Agente TikZ en Windows...

REM Verificar que Python esté instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python no encontrado. Instala desde https://python.org/
    pause
    exit /b 1
)

REM Instalar librerías Python globalmente
pip install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

echo ✅ Instalación completada para Windows
pause
"""
        
        # Guardar scripts
        scripts_dir = Path(__file__).parent / "scripts_instalacion"
        scripts_dir.mkdir(exist_ok=True)
        
        with open(scripts_dir / "instalar_ubuntu.sh", 'w') as f:
            f.write(script_ubuntu)
        
        with open(scripts_dir / "instalar_macos.sh", 'w') as f:
            f.write(script_macos)
        
        with open(scripts_dir / "instalar_windows.bat", 'w') as f:
            f.write(script_windows)
        
        # Hacer ejecutables los scripts de Unix
        try:
            os.chmod(scripts_dir / "instalar_ubuntu.sh", 0o755)
            os.chmod(scripts_dir / "instalar_macos.sh", 0o755)
        except:
            pass
        
        print(f"📁 Scripts de instalación generados en: {scripts_dir}")

def main():
    """Función principal."""
    instalador = InstaladorDependenciasGlobales()
    
    print("🎨 AGENTE TIKZ - INSTALADOR DE DEPENDENCIAS GLOBALES")
    print("=" * 60)
    print("Este script instalará todas las dependencias necesarias")
    print("de manera GLOBAL en tu sistema (sin entornos virtuales)")
    print()
    
    # Preguntar sobre dependencias opcionales
    respuesta = input("¿Instalar también dependencias opcionales (TensorFlow, PyTorch)? (s/N): ")
    incluir_opcionales = respuesta.lower().startswith('s')
    
    # Ejecutar instalación
    exito = instalador.instalar_todas_dependencias(incluir_opcionales)
    
    # Generar scripts para otros sistemas
    instalador.generar_script_instalacion_sistema()
    
    if exito:
        print("\n🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!")
        print("🚀 El Agente TikZ está listo para usar")
    else:
        print("\n⚠️ Instalación completada con algunos errores")
        print("💡 Revisa los mensajes anteriores para solucionar problemas")

if __name__ == "__main__":
    main()
