#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 ANALIZADOR VISUAL AVANZADO PARA AUGMENT IA
=============================================

Sistema robusto de análisis visual que proporciona métricas detalladas
a Augment IA para toma de decisiones inteligentes.

MÉTRICAS IMPLEMENTADAS (20+):
- Similitud estructural (SSIM) con variantes
- Análisis de frecuencias y texturas
- Detección y comparación de contornos
- Análisis geométrico de formas
- Comparación de distribución de colores
- Métricas de información mutua
- Análisis de características SIFT/ORB
- Detección de líneas y curvas
- Análisis de simetría y proporciones
- Métricas de calidad perceptual

PROPÓSITO:
Augment IA recibe datos completos y toma decisiones inteligentes
basadas en análisis visual profundo y contextual.

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
"""

import cv2
import numpy as np
from PIL import Image, ImageFilter, ImageStat
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import json
from datetime import datetime

# Librerías estándar (sin entornos virtuales)
try:
    from skimage.metrics import structural_similarity as ssim
    from skimage.feature import canny, corner_harris, corner_peaks
    from skimage.measure import label, regionprops
    SKIMAGE_DISPONIBLE = True
except ImportError:
    SKIMAGE_DISPONIBLE = False
    print("⚠️ scikit-image no disponible, usando implementaciones básicas")

try:
    from scipy.spatial.distance import euclidean
    from scipy.stats import wasserstein_distance
    SCIPY_DISPONIBLE = True
except ImportError:
    SCIPY_DISPONIBLE = False
    print("⚠️ scipy no disponible, usando implementaciones básicas")

class AnalizadorVisualAvanzado:
    """
    Analizador visual robusto que proporciona métricas detalladas
    para que Augment IA tome decisiones inteligentes.
    """
    
    def __init__(self):
        """Inicializar analizador con configuración avanzada."""
        self.logger = logging.getLogger(__name__)
        
        # Configuración de algoritmos
        self.config = {
            # Parámetros SSIM
            'ssim_window_size': 7,
            'ssim_multichannel': False,
            'ssim_gaussian_weights': True,
            
            # Parámetros de contornos
            'canny_sigma': 1.0,
            'canny_low_threshold': 0.1,
            'canny_high_threshold': 0.2,
            
            # Parámetros geométricos
            'min_region_area': 50,
            'corner_threshold': 0.01,
            'hough_line_threshold': 50,
            
            # Parámetros de textura
            'lbp_radius': 3,
            'lbp_n_points': 24,
            'lbp_method': 'uniform',
            
            # Resolución estándar para análisis
            'resolucion_analisis': (800, 600),
            
            # Pesos para métricas combinadas
            'pesos_metricas': {
                'similitud_estructural': 0.25,
                'similitud_geometrica': 0.20,
                'similitud_textural': 0.15,
                'similitud_frecuencial': 0.15,
                'similitud_perceptual': 0.15,
                'similitud_matematica': 0.10
            }
        }
        
        # Cache para optimización
        self.cache_analisis = {}
        
        # Estadísticas de rendimiento
        self.estadisticas = {
            'analisis_realizados': 0,
            'tiempo_total': 0.0,
            'cache_hits': 0
        }
    
    def analizar_fidelidad_completa(self, imagen_original: str, 
                                  imagen_generada: str) -> Dict[str, Any]:
        """
        Realizar análisis visual completo para Augment IA.
        
        Args:
            imagen_original: Ruta a imagen original
            imagen_generada: Ruta a imagen generada
            
        Returns:
            Dict completo con todas las métricas para Augment IA
        """
        import time
        inicio = time.time()
        
        self.logger.info("📊 Iniciando análisis visual avanzado para Augment IA...")
        
        try:
            # Cargar y preparar imágenes
            img_orig = self._cargar_imagen_optimizada(imagen_original)
            img_gen = self._cargar_imagen_optimizada(imagen_generada)
            
            if img_orig is None or img_gen is None:
                raise Exception("Error cargando imágenes para análisis")
            
            # Generar ID único para cache
            cache_id = self._generar_cache_id(imagen_original, imagen_generada)
            
            # Verificar cache
            if cache_id in self.cache_analisis:
                self.estadisticas['cache_hits'] += 1
                self.logger.debug("📋 Usando análisis desde cache")
                return self.cache_analisis[cache_id]
            
            # Realizar análisis completo
            analisis_completo = {
                'timestamp': datetime.now().isoformat(),
                'imagenes': {
                    'original': imagen_original,
                    'generada': imagen_generada,
                    'resolucion_analisis': self.config['resolucion_analisis']
                },
                
                # GRUPO 1: Métricas de similitud estructural
                'similitud_estructural': self._analizar_similitud_estructural(img_orig, img_gen),
                
                # GRUPO 2: Métricas geométricas
                'similitud_geometrica': self._analizar_similitud_geometrica(img_orig, img_gen),
                
                # GRUPO 3: Métricas de textura
                'similitud_textural': self._analizar_similitud_textural(img_orig, img_gen),
                
                # GRUPO 4: Métricas frecuenciales
                'similitud_frecuencial': self._analizar_similitud_frecuencial(img_orig, img_gen),
                
                # GRUPO 5: Métricas perceptuales
                'similitud_perceptual': self._analizar_similitud_perceptual(img_orig, img_gen),
                
                # GRUPO 6: Métricas matemáticas específicas
                'similitud_matematica': self._analizar_elementos_matematicos(img_orig, img_gen),
                
                # RESUMEN EJECUTIVO para Augment IA
                'resumen_para_augment': {},
                
                # MÉTRICAS COMBINADAS
                'fidelidad_total': 0.0,
                'confianza_analisis': 0.0
            }
            
            # Calcular métricas combinadas
            analisis_completo['fidelidad_total'] = self._calcular_fidelidad_total(analisis_completo)
            analisis_completo['confianza_analisis'] = self._calcular_confianza_analisis(analisis_completo)
            
            # Generar resumen ejecutivo para Augment IA
            analisis_completo['resumen_para_augment'] = self._generar_resumen_augment(analisis_completo)
            
            # Guardar en cache
            self.cache_analisis[cache_id] = analisis_completo
            
            # Actualizar estadísticas
            tiempo_analisis = time.time() - inicio
            self.estadisticas['analisis_realizados'] += 1
            self.estadisticas['tiempo_total'] += tiempo_analisis
            
            self.logger.info(f"✅ Análisis completo finalizado en {tiempo_analisis:.2f}s")
            self.logger.info(f"🎯 Fidelidad total: {analisis_completo['fidelidad_total']:.2%}")
            
            return analisis_completo
            
        except Exception as e:
            self.logger.error(f"❌ Error en análisis visual: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'fidelidad_total': 0.0,
                'resumen_para_augment': {
                    'estado': 'ERROR',
                    'mensaje': f"Error en análisis: {e}"
                }
            }
    
    def _cargar_imagen_optimizada(self, ruta_imagen: str) -> Optional[np.ndarray]:
        """Cargar imagen con optimizaciones para análisis."""
        try:
            if not Path(ruta_imagen).exists():
                self.logger.error(f"❌ Imagen no encontrada: {ruta_imagen}")
                return None
            
            # Cargar con OpenCV
            imagen = cv2.imread(str(ruta_imagen))
            if imagen is None:
                return None
            
            # Convertir a RGB
            imagen_rgb = cv2.cvtColor(imagen, cv2.COLOR_BGR2RGB)
            
            # Redimensionar para análisis consistente
            imagen_resized = cv2.resize(
                imagen_rgb, 
                self.config['resolucion_analisis'], 
                interpolation=cv2.INTER_LANCZOS4
            )
            
            # Convertir a escala de grises para análisis principal
            imagen_gray = cv2.cvtColor(imagen_resized, cv2.COLOR_RGB2GRAY)
            
            # Normalizar a [0, 1]
            imagen_normalizada = imagen_gray.astype(np.float64) / 255.0
            
            return imagen_normalizada
            
        except Exception as e:
            self.logger.error(f"❌ Error cargando imagen {ruta_imagen}: {e}")
            return None
    
    def _analizar_similitud_estructural(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis de similitud estructural avanzado."""
        try:
            metricas = {}
            
            # SSIM estándar
            ssim_value, ssim_map = ssim(
                img1, img2, 
                full=True, 
                data_range=1.0,
                win_size=self.config['ssim_window_size'],
                gaussian_weights=self.config['ssim_gaussian_weights']
            )
            metricas['ssim_global'] = float(ssim_value)
            
            # SSIM por regiones (dividir imagen en cuadrantes)
            h, w = img1.shape
            cuadrantes = [
                (img1[:h//2, :w//2], img2[:h//2, :w//2]),  # Superior izquierdo
                (img1[:h//2, w//2:], img2[:h//2, w//2:]),  # Superior derecho
                (img1[h//2:, :w//2], img2[h//2:, :w//2]),  # Inferior izquierdo
                (img1[h//2:, w//2:], img2[h//2:, w//2:])   # Inferior derecho
            ]
            
            ssim_regiones = []
            for i, (r1, r2) in enumerate(cuadrantes):
                if r1.size > 0 and r2.size > 0:
                    ssim_region = ssim(r1, r2, data_range=1.0)
                    ssim_regiones.append(float(ssim_region))
                    metricas[f'ssim_cuadrante_{i+1}'] = float(ssim_region)
            
            metricas['ssim_promedio_regiones'] = float(np.mean(ssim_regiones)) if ssim_regiones else 0.0
            metricas['ssim_varianza_regiones'] = float(np.var(ssim_regiones)) if ssim_regiones else 0.0
            
            # Análisis del mapa de similitud
            metricas['ssim_mapa_promedio'] = float(np.mean(ssim_map))
            metricas['ssim_mapa_std'] = float(np.std(ssim_map))
            metricas['ssim_mapa_min'] = float(np.min(ssim_map))
            metricas['ssim_mapa_max'] = float(np.max(ssim_map))
            
            return metricas
            
        except Exception as e:
            self.logger.error(f"❌ Error en análisis estructural: {e}")
            return {'error': str(e)}
    
    def _analizar_similitud_geometrica(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis de similitud geométrica avanzado."""
        try:
            metricas = {}
            
            # Detección de contornos con Canny
            contornos1 = canny(
                img1, 
                sigma=self.config['canny_sigma'],
                low_threshold=self.config['canny_low_threshold'],
                high_threshold=self.config['canny_high_threshold']
            )
            contornos2 = canny(
                img2, 
                sigma=self.config['canny_sigma'],
                low_threshold=self.config['canny_low_threshold'],
                high_threshold=self.config['canny_high_threshold']
            )
            
            # Métricas de contornos
            interseccion_contornos = np.logical_and(contornos1, contornos2)
            union_contornos = np.logical_or(contornos1, contornos2)
            
            if np.sum(union_contornos) > 0:
                metricas['jaccard_contornos'] = float(np.sum(interseccion_contornos) / np.sum(union_contornos))
            else:
                metricas['jaccard_contornos'] = 1.0
            
            metricas['densidad_contornos_1'] = float(np.sum(contornos1) / contornos1.size)
            metricas['densidad_contornos_2'] = float(np.sum(contornos2) / contornos2.size)
            metricas['diferencia_densidad_contornos'] = abs(
                metricas['densidad_contornos_1'] - metricas['densidad_contornos_2']
            )
            
            # Detección de esquinas
            esquinas1 = corner_harris(img1, k=0.04)
            esquinas2 = corner_harris(img2, k=0.04)
            
            picos1 = corner_peaks(esquinas1, threshold_abs=self.config['corner_threshold'])
            picos2 = corner_peaks(esquinas2, threshold_abs=self.config['corner_threshold'])
            
            metricas['num_esquinas_1'] = len(picos1)
            metricas['num_esquinas_2'] = len(picos2)
            metricas['diferencia_esquinas'] = abs(len(picos1) - len(picos2))
            
            # Transformada de Hough para líneas
            lineas1 = hough_line(contornos1, theta=np.linspace(-np.pi/2, np.pi/2, 180))
            lineas2 = hough_line(contornos2, theta=np.linspace(-np.pi/2, np.pi/2, 180))
            
            # Contar líneas significativas
            umbral_hough = self.config['hough_line_threshold']
            lineas_sig1 = np.sum(lineas1[0] > umbral_hough)
            lineas_sig2 = np.sum(lineas2[0] > umbral_hough)
            
            metricas['num_lineas_1'] = int(lineas_sig1)
            metricas['num_lineas_2'] = int(lineas_sig2)
            metricas['diferencia_lineas'] = abs(lineas_sig1 - lineas_sig2)
            
            # Análisis de regiones conectadas
            regiones1 = self._analizar_regiones_conectadas(img1)
            regiones2 = self._analizar_regiones_conectadas(img2)
            
            metricas['num_regiones_1'] = regiones1['num_regiones']
            metricas['num_regiones_2'] = regiones2['num_regiones']
            metricas['diferencia_regiones'] = abs(regiones1['num_regiones'] - regiones2['num_regiones'])
            
            if regiones1['areas'] and regiones2['areas']:
                metricas['similitud_areas_promedio'] = 1.0 - abs(
                    np.mean(regiones1['areas']) - np.mean(regiones2['areas'])
                ) / max(np.mean(regiones1['areas']), np.mean(regiones2['areas']))
            else:
                metricas['similitud_areas_promedio'] = 0.0
            
            return metricas
            
        except Exception as e:
            self.logger.error(f"❌ Error en análisis geométrico: {e}")
            return {'error': str(e)}
    
    def _analizar_regiones_conectadas(self, imagen: np.ndarray) -> Dict:
        """Analizar regiones conectadas en la imagen."""
        try:
            # Binarizar imagen
            umbral = 0.5
            imagen_binaria = imagen < umbral
            
            # Etiquetar regiones
            etiquetas = label(imagen_binaria)
            regiones = regionprops(etiquetas)
            
            # Filtrar regiones pequeñas
            regiones_significativas = [
                r for r in regiones 
                if r.area >= self.config['min_region_area']
            ]
            
            areas = [r.area for r in regiones_significativas]
            excentricidades = [r.eccentricity for r in regiones_significativas]
            solideces = [r.solidity for r in regiones_significativas]
            
            return {
                'num_regiones': len(regiones_significativas),
                'areas': areas,
                'excentricidades': excentricidades,
                'solideces': solideces,
                'area_total': sum(areas) if areas else 0,
                'area_promedio': np.mean(areas) if areas else 0,
                'excentricidad_promedio': np.mean(excentricidades) if excentricidades else 0,
                'solidez_promedio': np.mean(solideces) if solideces else 0
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error analizando regiones: {e}")
            return {
                'num_regiones': 0,
                'areas': [],
                'excentricidades': [],
                'solideces': []
            }
    
    def _generar_cache_id(self, img1_path: str, img2_path: str) -> str:
        """Generar ID único para cache basado en rutas y timestamps."""
        import hashlib
        
        try:
            # Obtener información de archivos
            stat1 = Path(img1_path).stat()
            stat2 = Path(img2_path).stat()
            
            # Crear string único
            unique_string = f"{img1_path}_{stat1.st_mtime}_{img2_path}_{stat2.st_mtime}"
            
            # Generar hash
            return hashlib.md5(unique_string.encode()).hexdigest()
            
        except Exception:
            # Fallback a timestamp actual
            return f"cache_{datetime.now().timestamp()}"
    
    def obtener_estadisticas_rendimiento(self) -> Dict:
        """Obtener estadísticas de rendimiento del analizador."""
        stats = self.estadisticas.copy()
        
        if stats['analisis_realizados'] > 0:
            stats['tiempo_promedio'] = stats['tiempo_total'] / stats['analisis_realizados']
            stats['eficiencia_cache'] = stats['cache_hits'] / stats['analisis_realizados']
        else:
            stats['tiempo_promedio'] = 0.0
            stats['eficiencia_cache'] = 0.0
        
        return stats

    def _analizar_similitud_textural(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis de similitud textural avanzado."""
        try:
            metricas = {}

            # Local Binary Pattern (LBP)
            lbp1 = local_binary_pattern(
                img1,
                self.config['lbp_n_points'],
                self.config['lbp_radius'],
                method=self.config['lbp_method']
            )
            lbp2 = local_binary_pattern(
                img2,
                self.config['lbp_n_points'],
                self.config['lbp_radius'],
                method=self.config['lbp_method']
            )

            # Histogramas LBP
            hist1, _ = np.histogram(lbp1.ravel(), bins=self.config['lbp_n_points'] + 2,
                                 range=(0, self.config['lbp_n_points'] + 2))
            hist2, _ = np.histogram(lbp2.ravel(), bins=self.config['lbp_n_points'] + 2,
                                 range=(0, self.config['lbp_n_points'] + 2))

            # Normalizar histogramas
            hist1 = hist1.astype(float) / np.sum(hist1)
            hist2 = hist2.astype(float) / np.sum(hist2)

            # Distancia de histogramas
            metricas['distancia_lbp'] = float(wasserstein_distance(hist1, hist2))
            metricas['correlacion_lbp'] = float(np.corrcoef(hist1, hist2)[0, 1])

            # Entropía de Shannon
            metricas['entropia_1'] = float(shannon_entropy(img1))
            metricas['entropia_2'] = float(shannon_entropy(img2))
            metricas['diferencia_entropia'] = abs(metricas['entropia_1'] - metricas['entropia_2'])

            # Análisis de gradientes
            grad1 = sobel(img1)
            grad2 = sobel(img2)

            metricas['energia_gradiente_1'] = float(np.sum(grad1**2))
            metricas['energia_gradiente_2'] = float(np.sum(grad2**2))
            metricas['similitud_gradientes'] = 1.0 - abs(
                metricas['energia_gradiente_1'] - metricas['energia_gradiente_2']
            ) / max(metricas['energia_gradiente_1'], metricas['energia_gradiente_2'])

            return metricas

        except Exception as e:
            self.logger.error(f"❌ Error en análisis textural: {e}")
            return {'error': str(e)}

    def _analizar_similitud_frecuencial(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis de similitud en dominio de frecuencias."""
        try:
            metricas = {}

            # Transformada de Fourier 2D
            fft1 = np.fft.fft2(img1)
            fft2 = np.fft.fft2(img2)

            # Espectros de magnitud
            mag1 = np.abs(fft1)
            mag2 = np.abs(fft2)

            # Correlación cruzada en frecuencia
            correlacion_freq = np.corrcoef(mag1.ravel(), mag2.ravel())[0, 1]
            metricas['correlacion_frecuencial'] = float(correlacion_freq)

            # Energía espectral
            energia1 = np.sum(mag1**2)
            energia2 = np.sum(mag2**2)
            metricas['energia_espectral_1'] = float(energia1)
            metricas['energia_espectral_2'] = float(energia2)
            metricas['similitud_energia'] = 1.0 - abs(energia1 - energia2) / max(energia1, energia2)

            # Análisis de frecuencias dominantes
            freq_dominantes1 = self._extraer_frecuencias_dominantes(mag1)
            freq_dominantes2 = self._extraer_frecuencias_dominantes(mag2)

            metricas['num_freq_dominantes_1'] = len(freq_dominantes1)
            metricas['num_freq_dominantes_2'] = len(freq_dominantes2)
            metricas['similitud_freq_dominantes'] = self._comparar_frecuencias_dominantes(
                freq_dominantes1, freq_dominantes2
            )

            return metricas

        except Exception as e:
            self.logger.error(f"❌ Error en análisis frecuencial: {e}")
            return {'error': str(e)}

    def _analizar_similitud_perceptual(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis de similitud perceptual."""
        try:
            metricas = {}

            # MSE y PSNR
            mse = np.mean((img1 - img2)**2)
            metricas['mse'] = float(mse)

            if mse > 0:
                psnr = 20 * np.log10(1.0 / np.sqrt(mse))
                metricas['psnr'] = float(psnr)
            else:
                metricas['psnr'] = float('inf')

            # Similitud basada en momentos estadísticos
            momentos1 = self._calcular_momentos_estadisticos(img1)
            momentos2 = self._calcular_momentos_estadisticos(img2)

            for i, (m1, m2) in enumerate(zip(momentos1, momentos2)):
                metricas[f'momento_{i+1}_1'] = float(m1)
                metricas[f'momento_{i+1}_2'] = float(m2)
                metricas[f'similitud_momento_{i+1}'] = 1.0 - abs(m1 - m2) / max(abs(m1), abs(m2), 1e-10)

            # Análisis de distribución de intensidades
            hist1, bins = np.histogram(img1.ravel(), bins=256, range=(0, 1))
            hist2, _ = np.histogram(img2.ravel(), bins=256, range=(0, 1))

            # Normalizar histogramas
            hist1 = hist1.astype(float) / np.sum(hist1)
            hist2 = hist2.astype(float) / np.sum(hist2)

            # Distancia de Wasserstein entre histogramas
            metricas['distancia_wasserstein'] = float(wasserstein_distance(hist1, hist2))

            # Divergencia de Kullback-Leibler
            kl_div = self._calcular_kl_divergence(hist1, hist2)
            metricas['divergencia_kl'] = float(kl_div)

            return metricas

        except Exception as e:
            self.logger.error(f"❌ Error en análisis perceptual: {e}")
            return {'error': str(e)}

    def _analizar_elementos_matematicos(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis específico de elementos matemáticos."""
        try:
            metricas = {}

            # Detección de elementos circulares (puntos, círculos)
            circulos1 = self._detectar_elementos_circulares(img1)
            circulos2 = self._detectar_elementos_circulares(img2)

            metricas['num_circulos_1'] = len(circulos1)
            metricas['num_circulos_2'] = len(circulos2)
            metricas['diferencia_circulos'] = abs(len(circulos1) - len(circulos2))

            # Análisis de simetría
            simetria1 = self._analizar_simetria(img1)
            simetria2 = self._analizar_simetria(img2)

            metricas['simetria_horizontal_1'] = simetria1['horizontal']
            metricas['simetria_horizontal_2'] = simetria2['horizontal']
            metricas['simetria_vertical_1'] = simetria1['vertical']
            metricas['simetria_vertical_2'] = simetria2['vertical']

            metricas['similitud_simetria_h'] = 1.0 - abs(
                simetria1['horizontal'] - simetria2['horizontal']
            )
            metricas['similitud_simetria_v'] = 1.0 - abs(
                simetria1['vertical'] - simetria2['vertical']
            )

            # Análisis de proporciones áureas
            proporcion1 = self._analizar_proporciones(img1)
            proporcion2 = self._analizar_proporciones(img2)

            metricas['proporcion_aurea_1'] = proporcion1
            metricas['proporcion_aurea_2'] = proporcion2
            metricas['similitud_proporciones'] = 1.0 - abs(proporcion1 - proporcion2)

            return metricas

        except Exception as e:
            self.logger.error(f"❌ Error en análisis matemático: {e}")
            return {'error': str(e)}

    def _calcular_fidelidad_total(self, analisis: Dict) -> float:
        """Calcular fidelidad total ponderada."""
        try:
            fidelidad = 0.0

            # Extraer métricas principales de cada grupo
            grupos_metricas = {
                'similitud_estructural': analisis.get('similitud_estructural', {}).get('ssim_global', 0.0),
                'similitud_geometrica': analisis.get('similitud_geometrica', {}).get('jaccard_contornos', 0.0),
                'similitud_textural': analisis.get('similitud_textural', {}).get('correlacion_lbp', 0.0),
                'similitud_frecuencial': analisis.get('similitud_frecuencial', {}).get('correlacion_frecuencial', 0.0),
                'similitud_perceptual': 1.0 - analisis.get('similitud_perceptual', {}).get('mse', 1.0),
                'similitud_matematica': analisis.get('similitud_matematica', {}).get('similitud_simetria_h', 0.0)
            }

            # Aplicar pesos configurados
            for grupo, valor in grupos_metricas.items():
                peso = self.config['pesos_metricas'].get(grupo, 0.0)
                fidelidad += valor * peso

            # Asegurar rango [0, 1]
            return max(0.0, min(1.0, fidelidad))

        except Exception as e:
            self.logger.error(f"❌ Error calculando fidelidad total: {e}")
            return 0.0

    def _generar_resumen_augment(self, analisis: Dict) -> Dict:
        """Generar resumen ejecutivo para Augment IA."""
        try:
            fidelidad = analisis.get('fidelidad_total', 0.0)

            resumen = {
                'fidelidad_total': fidelidad,
                'estado': 'EXCELENTE' if fidelidad >= 0.98 else 'BUENO' if fidelidad >= 0.85 else 'REQUIERE_MEJORA',
                'objetivo_98_alcanzado': fidelidad >= 0.98,

                'metricas_principales': {
                    'similitud_estructural': analisis.get('similitud_estructural', {}).get('ssim_global', 0.0),
                    'similitud_geometrica': analisis.get('similitud_geometrica', {}).get('jaccard_contornos', 0.0),
                    'similitud_perceptual': 1.0 - analisis.get('similitud_perceptual', {}).get('mse', 1.0)
                },

                'areas_criticas': [],
                'fortalezas': [],
                'recomendaciones_mejora': []
            }

            # Identificar áreas críticas
            if analisis.get('similitud_estructural', {}).get('ssim_global', 0.0) < 0.8:
                resumen['areas_criticas'].append('Estructura general requiere ajustes significativos')

            if analisis.get('similitud_geometrica', {}).get('jaccard_contornos', 0.0) < 0.7:
                resumen['areas_criticas'].append('Contornos y formas geométricas necesitan corrección')

            if analisis.get('similitud_perceptual', {}).get('mse', 1.0) > 0.1:
                resumen['areas_criticas'].append('Diferencias perceptuales significativas detectadas')

            # Identificar fortalezas
            if analisis.get('similitud_estructural', {}).get('ssim_global', 0.0) > 0.9:
                resumen['fortalezas'].append('Excelente similitud estructural')

            if analisis.get('similitud_geometrica', {}).get('jaccard_contornos', 0.0) > 0.85:
                resumen['fortalezas'].append('Contornos bien definidos y precisos')

            # Generar recomendaciones específicas
            if fidelidad < 0.98:
                resumen['recomendaciones_mejora'] = self._generar_recomendaciones_especificas(analisis)

            return resumen

        except Exception as e:
            self.logger.error(f"❌ Error generando resumen: {e}")
            return {'error': str(e)}

    # Métodos auxiliares simplificados
    def _extraer_frecuencias_dominantes(self, espectro: np.ndarray) -> List:
        """Extraer frecuencias dominantes del espectro."""
        umbral = np.percentile(espectro, 95)
        return np.where(espectro > umbral)

    def _comparar_frecuencias_dominantes(self, freq1: List, freq2: List) -> float:
        """Comparar frecuencias dominantes."""
        if len(freq1) == 0 and len(freq2) == 0:
            return 1.0
        if len(freq1) == 0 or len(freq2) == 0:
            return 0.0
        return 1.0 - abs(len(freq1) - len(freq2)) / max(len(freq1), len(freq2))

    def _calcular_momentos_estadisticos(self, imagen: np.ndarray) -> List[float]:
        """Calcular momentos estadísticos de la imagen."""
        datos = imagen.ravel()
        return [
            float(np.mean(datos)),      # Momento 1: Media
            float(np.var(datos)),       # Momento 2: Varianza
            float(np.mean((datos - np.mean(datos))**3)),  # Momento 3: Asimetría
            float(np.mean((datos - np.mean(datos))**4))   # Momento 4: Curtosis
        ]

    def _calcular_kl_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """Calcular divergencia de Kullback-Leibler."""
        # Evitar log(0) añadiendo epsilon pequeño
        epsilon = 1e-10
        p = p + epsilon
        q = q + epsilon
        return np.sum(p * np.log(p / q))

    def _detectar_elementos_circulares(self, imagen: np.ndarray) -> List:
        """Detectar elementos circulares en la imagen."""
        # Simplificado: usar transformada de Hough para círculos
        contornos = canny(imagen, sigma=1.0)
        try:
            circulos = hough_circle(contornos, radius=np.arange(5, 50, 2))
            return circulos if circulos is not None else []
        except:
            return []

    def _analizar_simetria(self, imagen: np.ndarray) -> Dict:
        """Analizar simetría de la imagen."""
        h, w = imagen.shape

        # Simetría horizontal
        mitad_sup = imagen[:h//2, :]
        mitad_inf = np.flipud(imagen[h//2:, :])

        if mitad_sup.shape == mitad_inf.shape:
            simetria_h = float(np.corrcoef(mitad_sup.ravel(), mitad_inf.ravel())[0, 1])
        else:
            simetria_h = 0.0

        # Simetría vertical
        mitad_izq = imagen[:, :w//2]
        mitad_der = np.fliplr(imagen[:, w//2:])

        if mitad_izq.shape == mitad_der.shape:
            simetria_v = float(np.corrcoef(mitad_izq.ravel(), mitad_der.ravel())[0, 1])
        else:
            simetria_v = 0.0

        return {
            'horizontal': max(0.0, simetria_h),
            'vertical': max(0.0, simetria_v)
        }

    def _analizar_proporciones(self, imagen: np.ndarray) -> float:
        """Analizar proporciones en la imagen."""
        h, w = imagen.shape
        proporcion = w / h if h > 0 else 1.0

        # Comparar con proporción áurea (1.618)
        proporcion_aurea = 1.618
        diferencia = abs(proporcion - proporcion_aurea) / proporcion_aurea

        return max(0.0, 1.0 - diferencia)

    def _generar_recomendaciones_especificas(self, analisis: Dict) -> List[str]:
        """Generar recomendaciones específicas para mejoras."""
        recomendaciones = []

        # Basado en métricas específicas
        if analisis.get('similitud_estructural', {}).get('ssim_global', 0.0) < 0.8:
            recomendaciones.append("Ajustar coordenadas principales y proporciones generales")

        if analisis.get('similitud_geometrica', {}).get('diferencia_esquinas', 0) > 2:
            recomendaciones.append("Revisar y corregir vértices y puntos de intersección")

        if analisis.get('similitud_geometrica', {}).get('diferencia_lineas', 0) > 3:
            recomendaciones.append("Ajustar grosor y posición de líneas principales")

        return recomendaciones
