#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 COMPARADOR VISUAL PARA AGENTE TIKZ RECURSIVO
===============================================

Módulo especializado en calcular métricas de fidelidad visual entre
la imagen original y las imágenes generadas por código TikZ.

Métricas implementadas:
- SSIM (Structural Similarity Index)
- MSE (Mean Squared Error)
- Análisis de contornos y formas
- Detección de elementos geométricos
- Comparación de características matemáticas

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
import logging
from typing import Dict, Tuple, List, Optional
from pathlib import Path
from skimage.metrics import structural_similarity as ssim
from skimage.feature import canny
from skimage.measure import label, regionprops
import matplotlib.pyplot as plt

class ComparadorVisual:
    """
    Comparador visual avanzado para calcular fidelidad entre imágenes.
    
    Utiliza múltiples métricas para evaluar la similitud visual y
    proporcionar retroalimentación detallada para mejoras.
    """
    
    def __init__(self):
        """Inicializar el comparador visual."""
        self.logger = logging.getLogger(__name__)
        
        # Configuración de parámetros
        self.config = {
            'tamaño_estandar': (800, 600),  # Tamaño para normalización
            'umbral_contorno': 50,          # Umbral para detección de contornos
            'sigma_gaussian': 1.0,          # Suavizado gaussiano
            'peso_ssim': 0.4,              # Peso de SSIM en fidelidad total
            'peso_mse': 0.2,               # Peso de MSE en fidelidad total
            'peso_contornos': 0.25,        # Peso de similitud de contornos
            'peso_elementos': 0.15         # Peso de elementos geométricos
        }
    
    def calcular_fidelidad_completa(self, imagen_original: str, 
                                  imagen_generada: str) -> Dict:
        """
        Calcular fidelidad completa entre imagen original y generada.
        
        Args:
            imagen_original: Ruta a la imagen original
            imagen_generada: Ruta a la imagen generada por TikZ
            
        Returns:
            Dict con todas las métricas de fidelidad calculadas
        """
        self.logger.info("📊 Calculando fidelidad visual completa...")
        
        try:
            # Cargar y normalizar imágenes
            img_orig = self._cargar_y_normalizar_imagen(imagen_original)
            img_gen = self._cargar_y_normalizar_imagen(imagen_generada)
            
            if img_orig is None or img_gen is None:
                raise Exception("Error cargando imágenes para comparación")
            
            # Calcular métricas individuales
            metricas = {}
            
            # 1. SSIM (Structural Similarity Index)
            metricas['ssim'] = self._calcular_ssim(img_orig, img_gen)
            
            # 2. MSE (Mean Squared Error) normalizado
            metricas['mse_normalizado'] = self._calcular_mse_normalizado(img_orig, img_gen)
            
            # 3. Similitud de contornos
            metricas['similitud_contornos'] = self._calcular_similitud_contornos(img_orig, img_gen)
            
            # 4. Análisis de elementos geométricos
            metricas['similitud_elementos'] = self._analizar_elementos_geometricos(img_orig, img_gen)
            
            # 5. Calcular fidelidad total ponderada
            metricas['fidelidad_total'] = self._calcular_fidelidad_total(metricas)
            
            # 6. Análisis adicional
            metricas['analisis_detallado'] = self._generar_analisis_detallado(
                img_orig, img_gen, metricas
            )
            
            self.logger.info(f"✅ Fidelidad calculada: {metricas['fidelidad_total']:.2%}")
            
            return metricas
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando fidelidad: {e}")
            return {
                'error': str(e),
                'fidelidad_total': 0.0
            }
    
    def _cargar_y_normalizar_imagen(self, ruta_imagen: str) -> Optional[np.ndarray]:
        """
        Cargar imagen y normalizarla para comparación.
        
        Args:
            ruta_imagen: Ruta a la imagen
            
        Returns:
            Array numpy con la imagen normalizada o None si hay error
        """
        try:
            # Cargar imagen
            if not Path(ruta_imagen).exists():
                self.logger.error(f"❌ Imagen no encontrada: {ruta_imagen}")
                return None
            
            # Leer con OpenCV
            imagen = cv2.imread(str(ruta_imagen))
            if imagen is None:
                self.logger.error(f"❌ Error leyendo imagen: {ruta_imagen}")
                return None
            
            # Convertir a RGB
            imagen_rgb = cv2.cvtColor(imagen, cv2.COLOR_BGR2RGB)
            
            # Redimensionar a tamaño estándar
            imagen_resized = cv2.resize(
                imagen_rgb, 
                self.config['tamaño_estandar'], 
                interpolation=cv2.INTER_LANCZOS4
            )
            
            # Convertir a escala de grises para análisis
            imagen_gray = cv2.cvtColor(imagen_resized, cv2.COLOR_RGB2GRAY)
            
            # Normalizar valores a [0, 1]
            imagen_normalizada = imagen_gray.astype(np.float64) / 255.0
            
            return imagen_normalizada
            
        except Exception as e:
            self.logger.error(f"❌ Error normalizando imagen {ruta_imagen}: {e}")
            return None
    
    def _calcular_ssim(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """
        Calcular Structural Similarity Index (SSIM).
        
        SSIM mide la similitud estructural entre imágenes considerando
        luminancia, contraste y estructura.
        """
        try:
            # Calcular SSIM
            ssim_value, _ = ssim(img1, img2, full=True, data_range=1.0)
            
            # SSIM está en rango [-1, 1], convertir a [0, 1]
            ssim_normalizado = (ssim_value + 1) / 2
            
            self.logger.debug(f"📊 SSIM calculado: {ssim_normalizado:.4f}")
            return float(ssim_normalizado)
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando SSIM: {e}")
            return 0.0
    
    def _calcular_mse_normalizado(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """
        Calcular Mean Squared Error normalizado.
        
        MSE mide la diferencia promedio cuadrática entre píxeles.
        Se normaliza para obtener una métrica de similitud [0, 1].
        """
        try:
            # Calcular MSE
            mse = np.mean((img1 - img2) ** 2)
            
            # Normalizar MSE a similitud [0, 1]
            # MSE máximo posible es 1.0 (diferencia total)
            similitud_mse = 1.0 - min(mse, 1.0)
            
            self.logger.debug(f"📊 MSE normalizado: {similitud_mse:.4f}")
            return float(similitud_mse)
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando MSE: {e}")
            return 0.0
    
    def _calcular_similitud_contornos(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """
        Calcular similitud basada en contornos detectados.
        
        Analiza la similitud de formas y estructuras geométricas
        mediante detección de bordes y contornos.
        """
        try:
            # Detectar contornos con Canny
            contornos1 = canny(img1, sigma=self.config['sigma_gaussian'])
            contornos2 = canny(img2, sigma=self.config['sigma_gaussian'])
            
            # Calcular intersección y unión de contornos
            interseccion = np.logical_and(contornos1, contornos2)
            union = np.logical_or(contornos1, contornos2)
            
            # Calcular Jaccard Index (IoU para contornos)
            if np.sum(union) == 0:
                similitud_contornos = 1.0  # Ambas imágenes sin contornos
            else:
                similitud_contornos = np.sum(interseccion) / np.sum(union)
            
            self.logger.debug(f"📊 Similitud contornos: {similitud_contornos:.4f}")
            return float(similitud_contornos)
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando similitud de contornos: {e}")
            return 0.0
    
    def _analizar_elementos_geometricos(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """
        Analizar similitud de elementos geométricos específicos.
        
        Detecta y compara formas, líneas, círculos y otros elementos
        geométricos típicos en figuras matemáticas.
        """
        try:
            # Detectar regiones conectadas
            regiones1 = self._detectar_regiones_geometricas(img1)
            regiones2 = self._detectar_regiones_geometricas(img2)
            
            # Comparar características de regiones
            similitud_regiones = self._comparar_regiones(regiones1, regiones2)
            
            # Detectar líneas con transformada de Hough
            similitud_lineas = self._comparar_lineas(img1, img2)
            
            # Combinar métricas de elementos geométricos
            similitud_elementos = (similitud_regiones + similitud_lineas) / 2
            
            self.logger.debug(f"📊 Similitud elementos: {similitud_elementos:.4f}")
            return float(similitud_elementos)
            
        except Exception as e:
            self.logger.error(f"❌ Error analizando elementos geométricos: {e}")
            return 0.0
    
    def _detectar_regiones_geometricas(self, imagen: np.ndarray) -> List[Dict]:
        """Detectar regiones geométricas en la imagen."""
        try:
            # Binarizar imagen
            umbral = 0.5
            imagen_binaria = imagen < umbral
            
            # Etiquetar regiones conectadas
            etiquetas = label(imagen_binaria)
            regiones = regionprops(etiquetas)
            
            # Extraer características de cada región
            caracteristicas_regiones = []
            for region in regiones:
                if region.area > 50:  # Filtrar regiones muy pequeñas
                    caracteristicas_regiones.append({
                        'area': region.area,
                        'perimetro': region.perimeter,
                        'excentricidad': region.eccentricity,
                        'solidez': region.solidity,
                        'bbox': region.bbox
                    })
            
            return caracteristicas_regiones
            
        except Exception as e:
            self.logger.error(f"❌ Error detectando regiones: {e}")
            return []
    
    def _comparar_regiones(self, regiones1: List[Dict], regiones2: List[Dict]) -> float:
        """Comparar características de regiones detectadas."""
        try:
            if not regiones1 and not regiones2:
                return 1.0
            
            if not regiones1 or not regiones2:
                return 0.0
            
            # Comparar número de regiones
            num_regiones1 = len(regiones1)
            num_regiones2 = len(regiones2)
            
            similitud_cantidad = 1.0 - abs(num_regiones1 - num_regiones2) / max(num_regiones1, num_regiones2)
            
            # Comparar características promedio
            if regiones1 and regiones2:
                areas1 = [r['area'] for r in regiones1]
                areas2 = [r['area'] for r in regiones2]
                
                area_promedio1 = np.mean(areas1)
                area_promedio2 = np.mean(areas2)
                
                if max(area_promedio1, area_promedio2) > 0:
                    similitud_areas = 1.0 - abs(area_promedio1 - area_promedio2) / max(area_promedio1, area_promedio2)
                else:
                    similitud_areas = 1.0
            else:
                similitud_areas = 0.0
            
            return (similitud_cantidad + similitud_areas) / 2
            
        except Exception as e:
            self.logger.error(f"❌ Error comparando regiones: {e}")
            return 0.0
    
    def _comparar_lineas(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """Comparar líneas detectadas con transformada de Hough."""
        try:
            # Detectar bordes
            bordes1 = canny(img1, sigma=1.0)
            bordes2 = canny(img2, sigma=1.0)
            
            # Convertir a uint8 para Hough
            bordes1_uint8 = (bordes1 * 255).astype(np.uint8)
            bordes2_uint8 = (bordes2 * 255).astype(np.uint8)
            
            # Detectar líneas con Hough
            lineas1 = cv2.HoughLines(bordes1_uint8, 1, np.pi/180, threshold=50)
            lineas2 = cv2.HoughLines(bordes2_uint8, 1, np.pi/180, threshold=50)
            
            # Comparar número de líneas detectadas
            num_lineas1 = len(lineas1) if lineas1 is not None else 0
            num_lineas2 = len(lineas2) if lineas2 is not None else 0
            
            if num_lineas1 == 0 and num_lineas2 == 0:
                return 1.0
            
            if num_lineas1 == 0 or num_lineas2 == 0:
                return 0.0
            
            # Similitud basada en cantidad de líneas
            similitud_lineas = 1.0 - abs(num_lineas1 - num_lineas2) / max(num_lineas1, num_lineas2)
            
            return similitud_lineas
            
        except Exception as e:
            self.logger.error(f"❌ Error comparando líneas: {e}")
            return 0.0
    
    def _calcular_fidelidad_total(self, metricas: Dict) -> float:
        """
        Calcular fidelidad total ponderada.
        
        Combina todas las métricas usando pesos configurados
        para obtener una medida única de fidelidad.
        """
        try:
            fidelidad_total = (
                metricas.get('ssim', 0.0) * self.config['peso_ssim'] +
                metricas.get('mse_normalizado', 0.0) * self.config['peso_mse'] +
                metricas.get('similitud_contornos', 0.0) * self.config['peso_contornos'] +
                metricas.get('similitud_elementos', 0.0) * self.config['peso_elementos']
            )
            
            # Asegurar que esté en rango [0, 1]
            fidelidad_total = max(0.0, min(1.0, fidelidad_total))
            
            return float(fidelidad_total)
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando fidelidad total: {e}")
            return 0.0
    
    def _generar_analisis_detallado(self, img1: np.ndarray, img2: np.ndarray, 
                                  metricas: Dict) -> Dict:
        """Generar análisis detallado de las diferencias encontradas."""
        try:
            analisis = {
                'resumen_metricas': {
                    'ssim': metricas.get('ssim', 0.0),
                    'mse_normalizado': metricas.get('mse_normalizado', 0.0),
                    'similitud_contornos': metricas.get('similitud_contornos', 0.0),
                    'similitud_elementos': metricas.get('similitud_elementos', 0.0)
                },
                'areas_mejora': [],
                'fortalezas': []
            }
            
            # Identificar áreas de mejora
            if metricas.get('ssim', 0.0) < 0.8:
                analisis['areas_mejora'].append('Estructura general requiere ajustes')
            
            if metricas.get('similitud_contornos', 0.0) < 0.7:
                analisis['areas_mejora'].append('Contornos y formas necesitan refinamiento')
            
            if metricas.get('similitud_elementos', 0.0) < 0.7:
                analisis['areas_mejora'].append('Elementos geométricos requieren corrección')
            
            # Identificar fortalezas
            if metricas.get('ssim', 0.0) > 0.9:
                analisis['fortalezas'].append('Excelente similitud estructural')
            
            if metricas.get('similitud_contornos', 0.0) > 0.8:
                analisis['fortalezas'].append('Contornos bien definidos')
            
            return analisis
            
        except Exception as e:
            self.logger.error(f"❌ Error generando análisis detallado: {e}")
            return {'error': str(e)}

def test_comparador():
    """Función de prueba para el comparador visual."""
    comparador = ComparadorVisual()
    
    # Crear imágenes de prueba
    img_test1 = np.random.rand(100, 100)
    img_test2 = img_test1 + np.random.rand(100, 100) * 0.1
    
    print("🧪 Probando comparador visual...")
    
    # Simular comparación
    ssim_test = comparador._calcular_ssim(img_test1, img_test2)
    mse_test = comparador._calcular_mse_normalizado(img_test1, img_test2)
    
    print(f"✅ SSIM: {ssim_test:.4f}")
    print(f"✅ MSE normalizado: {mse_test:.4f}")
    print("🎯 Comparador visual funcionando correctamente")

if __name__ == "__main__":
    test_comparador()
