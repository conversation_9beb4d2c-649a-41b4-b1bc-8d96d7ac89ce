# 📦 Instalación Global de Dependencias - Agente TikZ

## 🎯 **Objetivo**

Instalar todas las dependencias necesarias para el **Agente TikZ Recursivo** de manera **GLOBAL** en el sistema, **SIN usar entornos virtuales**.

## ✅ **Ventajas de la Instalación Global**

- 🚫 **Sin entornos virtuales** - No necesitas activar/desactivar nada
- 🔧 **Funciona directamente** con Augment IA en VSCode
- 🌍 **Disponible para todos los proyectos** del sistema
- ⚡ **Inicio rápido** - Sin configuración adicional
- 🔄 **Compatible con tasks de VSCode** automáticamente

---

## 🚀 **Instalación Automática (Recomendada)**

### **Método 1: Desde VSCode**
```
1. Ctrl+Shift+P
2. "Tasks: Run Task"
3. "📦 Agente TikZ: Instalar Dependencias Globales"
4. Seguir instrucciones en pantalla
```

### **Método 2: Desde Terminal**
```bash
cd /ruta/al/proyecto
python3 Auxiliares/Agente-Graficador-TikZ/02-Codigo-Recursivo/instalar_dependencias_globales.py
```

---

## 🛠️ **Instalación Manual por Sistema**

### **🐧 Ubuntu/Debian**
```bash
# Actualizar sistema
sudo apt update

# Instalar Python y herramientas básicas
sudo apt install -y python3 python3-pip python3-dev

# Instalar dependencias del sistema
sudo apt install -y texlive-latex-extra imagemagick ghostscript

# Instalar librerías Python globalmente
pip3 install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

# Verificar instalación
python3 -c "import cv2, numpy, PIL, skimage, scipy, matplotlib; print('✅ Todo instalado')"
```

### **🍎 macOS**
```bash
# Instalar Homebrew si no está instalado
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Instalar Python y herramientas
brew install python

# Instalar LaTeX y herramientas
brew install mactex imagemagick ghostscript

# Instalar librerías Python globalmente
pip3 install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

# Verificar instalación
python3 -c "import cv2, numpy, PIL, skimage, scipy, matplotlib; print('✅ Todo instalado')"
```

### **🪟 Windows**
```cmd
REM Descargar e instalar Python desde https://python.org/
REM Asegurarse de marcar "Add Python to PATH"

REM Instalar MiKTeX desde https://miktex.org/
REM Instalar ImageMagick desde https://imagemagick.org/

REM Instalar librerías Python globalmente
pip install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

REM Verificar instalación
python -c "import cv2, numpy, PIL, skimage, scipy, matplotlib; print('✅ Todo instalado')"
```

---

## 📋 **Dependencias Instaladas**

### **Librerías Principales**
| Librería | Propósito | Comando de Instalación |
|----------|-----------|------------------------|
| **OpenCV** | Procesamiento de imágenes | `pip3 install opencv-python` |
| **NumPy** | Computación numérica | `pip3 install numpy` |
| **Pillow** | Manipulación de imágenes | `pip3 install pillow` |
| **scikit-image** | Análisis avanzado de imágenes | `pip3 install scikit-image` |
| **SciPy** | Computación científica | `pip3 install scipy` |
| **Matplotlib** | Visualización | `pip3 install matplotlib` |

### **Herramientas del Sistema**
| Herramienta | Propósito | Instalación |
|-------------|-----------|-------------|
| **LaTeX** | Compilación TikZ | `texlive-latex-extra` |
| **ImageMagick** | Conversión PDF→PNG | `imagemagick` |
| **Ghostscript** | Alternativa de conversión | `ghostscript` |

---

## 🔍 **Verificación de Instalación**

### **Verificación Automática**
```bash
# Ejecutar task en VSCode:
"🔍 Agente TikZ: Verificar Dependencias Globales"
```

### **Verificación Manual**
```python
# Test completo de dependencias
python3 -c "
import cv2
import numpy as np
import PIL
import skimage
import scipy
import matplotlib

print('✅ OpenCV:', cv2.__version__)
print('✅ NumPy:', np.__version__)
print('✅ Pillow:', PIL.__version__)
print('✅ scikit-image:', skimage.__version__)
print('✅ SciPy:', scipy.__version__)
print('✅ Matplotlib:', matplotlib.__version__)
print('🎉 Todas las dependencias funcionando correctamente')
"
```

### **Test de Funcionalidad**
```python
# Test de análisis visual básico
python3 -c "
import cv2
import numpy as np
from skimage.metrics import structural_similarity as ssim

# Crear imágenes de prueba
img1 = np.random.rand(100, 100)
img2 = img1 + np.random.rand(100, 100) * 0.1

# Test SSIM
ssim_value = ssim(img1, img2, data_range=1.0)
print(f'✅ SSIM test: {ssim_value:.4f}')

# Test OpenCV
img_cv = (img1 * 255).astype(np.uint8)
contours = cv2.Canny(img_cv, 50, 150)
print(f'✅ OpenCV test: {np.sum(contours)} píxeles de contorno')

print('🎯 Sistema de análisis visual funcionando')
"
```

---

## ❌ **Solución de Problemas**

### **Error: "ModuleNotFoundError"**
```bash
# Reinstalar la librería específica
pip3 install --upgrade --force-reinstall nombre_libreria

# Ejemplo para OpenCV:
pip3 install --upgrade --force-reinstall opencv-python
```

### **Error: "Permission denied"**
```bash
# En Linux/macOS, usar --user si no tienes permisos de administrador
pip3 install --user opencv-python numpy pillow scikit-image scipy matplotlib
```

### **Error: "pip not found"**
```bash
# Ubuntu/Debian
sudo apt install python3-pip

# macOS
brew install python

# Windows: Reinstalar Python desde python.org
```

### **Error de compilación LaTeX**
```bash
# Ubuntu/Debian
sudo apt install texlive-full

# macOS
brew install --cask mactex

# Windows: Reinstalar MiKTeX completo
```

---

## 🎯 **Verificación Final**

Una vez instalado todo, ejecuta:

```bash
# Test completo del sistema
python3 Auxiliares/Agente-Graficador-TikZ/02-Codigo-Recursivo/agente_tikz_recursivo.py --test
```

Deberías ver:
```
✅ OpenCV disponible globalmente
✅ scikit-image disponible globalmente
✅ scipy disponible globalmente
✅ matplotlib disponible globalmente
✅ Sistema verificado correctamente
🎉 Agente TikZ listo para funcionar con 98% de fidelidad
```

---

## 🚀 **¡Listo para Usar!**

Con todas las dependencias instaladas globalmente, puedes:

1. **Ejecutar análisis recursivo:**
   ```
   Ctrl+Shift+P → "🔄 Agente TikZ: Revisión Recursiva 98% Fidelidad"
   ```

2. **Usar Augment IA directamente** sin configuración adicional

3. **Compilar TikZ automáticamente** con máxima calidad

4. **Obtener 98% de fidelidad garantizada** en tus figuras matemáticas

**🎨 ¡El Agente TikZ está listo para crear figuras perfectas! 🎯**
