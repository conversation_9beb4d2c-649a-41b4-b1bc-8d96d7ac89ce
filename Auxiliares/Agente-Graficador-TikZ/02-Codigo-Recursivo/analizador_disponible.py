#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 ANALIZADOR CON LIBRERÍAS DISPONIBLES
=======================================

Analizador que funciona con las librerías actualmente disponibles:
- NumPy ✅
- Pillow ✅  
- Matplotlib ✅

Proporciona análisis básico pero efectivo para Augment IA.

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
"""

import numpy as np
from PIL import Image, ImageFilter, ImageStat
import matplotlib.pyplot as plt
import logging
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

class AnalizadorDisponible:
    """
    Analizador que funciona con librerías disponibles actualmente.
    """
    
    def __init__(self):
        """Inicializar analizador."""
        self.logger = logging.getLogger(__name__)
        
        self.config = {
            'resolucion_analisis': (800, 600),
            'pesos_metricas': {
                'similitud_pixel': 0.40,
                'similitud_histograma': 0.30,
                'similitud_estadistica': 0.20,
                'similitud_estructura': 0.10
            }
        }
    
    def analizar_fidelidad_completa(self, imagen_original: str, 
                                  imagen_generada: str) -> Dict[str, Any]:
        """
        Análisis completo con librerías disponibles.
        """
        self.logger.info("📊 Iniciando análisis con librerías disponibles...")
        
        try:
            # Cargar imágenes
            img_orig = self._cargar_imagen_pil(imagen_original)
            img_gen = self._cargar_imagen_pil(imagen_generada)
            
            if img_orig is None or img_gen is None:
                raise Exception("Error cargando imágenes")
            
            # Convertir a arrays numpy
            arr_orig = np.array(img_orig)
            arr_gen = np.array(img_gen)
            
            # Realizar análisis
            analisis = {
                'timestamp': datetime.now().isoformat(),
                'metodo_analisis': 'LIBRERIAS_DISPONIBLES',
                'imagenes': {
                    'original': imagen_original,
                    'generada': imagen_generada
                },
                
                # Métricas disponibles
                'similitud_pixel': self._calcular_similitud_pixel(arr_orig, arr_gen),
                'similitud_histograma': self._calcular_similitud_histograma(img_orig, img_gen),
                'similitud_estadistica': self._calcular_similitud_estadistica(arr_orig, arr_gen),
                'similitud_estructura': self._calcular_similitud_estructura_basica(arr_orig, arr_gen),
                
                'fidelidad_total': 0.0,
                'confianza_analisis': 0.85,
                'resumen_para_augment': {}
            }
            
            # Calcular fidelidad total
            analisis['fidelidad_total'] = self._calcular_fidelidad_total(analisis)
            
            # Generar resumen para Augment
            analisis['resumen_para_augment'] = self._generar_resumen_augment(analisis)
            
            self.logger.info(f"✅ Análisis completado: {analisis['fidelidad_total']:.2%}")
            
            return analisis
            
        except Exception as e:
            self.logger.error(f"❌ Error en análisis: {e}")
            return {
                'error': str(e),
                'fidelidad_total': 0.0,
                'resumen_para_augment': {
                    'estado': 'ERROR',
                    'mensaje': f"Error en análisis: {e}"
                }
            }
    
    def _cargar_imagen_pil(self, ruta_imagen: str) -> Optional[Image.Image]:
        """Cargar imagen usando Pillow."""
        try:
            if not Path(ruta_imagen).exists():
                self.logger.error(f"❌ Imagen no encontrada: {ruta_imagen}")
                return None
            
            # Cargar con Pillow
            imagen = Image.open(ruta_imagen)
            
            # Convertir a RGB si es necesario
            if imagen.mode != 'RGB':
                imagen = imagen.convert('RGB')
            
            # Redimensionar
            imagen = imagen.resize(self.config['resolucion_analisis'], Image.LANCZOS)
            
            return imagen
            
        except Exception as e:
            self.logger.error(f"❌ Error cargando imagen: {e}")
            return None
    
    def _calcular_similitud_pixel(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Calcular similitud a nivel de píxel."""
        try:
            # Convertir a escala de grises
            if len(img1.shape) == 3:
                gray1 = np.mean(img1, axis=2)
                gray2 = np.mean(img2, axis=2)
            else:
                gray1, gray2 = img1, img2
            
            # Normalizar
            gray1 = gray1.astype(np.float64) / 255.0
            gray2 = gray2.astype(np.float64) / 255.0
            
            # MSE
            mse = np.mean((gray1 - gray2) ** 2)
            
            # PSNR
            if mse > 0:
                psnr = 20 * np.log10(1.0 / np.sqrt(mse))
            else:
                psnr = float('inf')
            
            # Similitud normalizada
            similitud = 1.0 - min(mse, 1.0)
            
            return {
                'mse': float(mse),
                'psnr': float(psnr),
                'similitud_pixel': float(similitud)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error en similitud pixel: {e}")
            return {'similitud_pixel': 0.0}
    
    def _calcular_similitud_histograma(self, img1: Image.Image, img2: Image.Image) -> Dict:
        """Calcular similitud de histogramas usando Pillow."""
        try:
            # Convertir a escala de grises
            gray1 = img1.convert('L')
            gray2 = img2.convert('L')
            
            # Calcular histogramas
            hist1 = gray1.histogram()
            hist2 = gray2.histogram()
            
            # Normalizar
            hist1 = np.array(hist1, dtype=float)
            hist2 = np.array(hist2, dtype=float)
            hist1 = hist1 / np.sum(hist1)
            hist2 = hist2 / np.sum(hist2)
            
            # Correlación
            correlacion = np.corrcoef(hist1, hist2)[0, 1]
            if np.isnan(correlacion):
                correlacion = 0.0
            
            # Intersección
            interseccion = np.sum(np.minimum(hist1, hist2))
            
            # Chi-cuadrado simplificado
            chi2 = np.sum((hist1 - hist2) ** 2 / (hist1 + hist2 + 1e-10))
            chi2_normalizado = 1.0 / (1.0 + chi2)
            
            return {
                'correlacion_histograma': float(correlacion),
                'interseccion_histograma': float(interseccion),
                'chi2_normalizado': float(chi2_normalizado),
                'similitud_histograma': float((correlacion + interseccion + chi2_normalizado) / 3)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error en similitud histograma: {e}")
            return {'similitud_histograma': 0.0}
    
    def _calcular_similitud_estadistica(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Calcular similitud estadística básica."""
        try:
            # Convertir a escala de grises
            if len(img1.shape) == 3:
                gray1 = np.mean(img1, axis=2)
                gray2 = np.mean(img2, axis=2)
            else:
                gray1, gray2 = img1, img2
            
            # Estadísticas básicas
            stats1 = {
                'media': np.mean(gray1),
                'std': np.std(gray1),
                'min': np.min(gray1),
                'max': np.max(gray1)
            }
            
            stats2 = {
                'media': np.mean(gray2),
                'std': np.std(gray2),
                'min': np.min(gray2),
                'max': np.max(gray2)
            }
            
            # Similitudes
            similitud_media = 1.0 - abs(stats1['media'] - stats2['media']) / 255.0
            similitud_std = 1.0 - abs(stats1['std'] - stats2['std']) / 255.0
            similitud_rango = 1.0 - abs((stats1['max'] - stats1['min']) - (stats2['max'] - stats2['min'])) / 255.0
            
            similitud_total = (similitud_media + similitud_std + similitud_rango) / 3
            
            return {
                'stats_imagen1': stats1,
                'stats_imagen2': stats2,
                'similitud_media': float(similitud_media),
                'similitud_std': float(similitud_std),
                'similitud_rango': float(similitud_rango),
                'similitud_estadistica': float(similitud_total)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error en similitud estadística: {e}")
            return {'similitud_estadistica': 0.0}
    
    def _calcular_similitud_estructura_basica(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Calcular similitud estructural básica usando NumPy."""
        try:
            # Convertir a escala de grises
            if len(img1.shape) == 3:
                gray1 = np.mean(img1, axis=2)
                gray2 = np.mean(img2, axis=2)
            else:
                gray1, gray2 = img1, img2
            
            # Normalizar
            gray1 = gray1.astype(np.float64) / 255.0
            gray2 = gray2.astype(np.float64) / 255.0
            
            # Gradientes simples
            grad_x1 = np.diff(gray1, axis=1)
            grad_y1 = np.diff(gray1, axis=0)
            grad_x2 = np.diff(gray2, axis=1)
            grad_y2 = np.diff(gray2, axis=0)
            
            # Similitud de gradientes
            if grad_x1.size > 0 and grad_x2.size > 0:
                similitud_grad_x = 1.0 - np.mean(np.abs(grad_x1 - grad_x2))
            else:
                similitud_grad_x = 1.0
                
            if grad_y1.size > 0 and grad_y2.size > 0:
                similitud_grad_y = 1.0 - np.mean(np.abs(grad_y1 - grad_y2))
            else:
                similitud_grad_y = 1.0
            
            similitud_estructura = (similitud_grad_x + similitud_grad_y) / 2
            
            return {
                'similitud_gradiente_x': float(max(0.0, similitud_grad_x)),
                'similitud_gradiente_y': float(max(0.0, similitud_grad_y)),
                'similitud_estructura': float(max(0.0, similitud_estructura))
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error en similitud estructura: {e}")
            return {'similitud_estructura': 0.0}
    
    def _calcular_fidelidad_total(self, analisis: Dict) -> float:
        """Calcular fidelidad total ponderada."""
        try:
            # Extraer métricas
            pixel_val = analisis.get('similitud_pixel', {}).get('similitud_pixel', 0.0)
            hist_val = analisis.get('similitud_histograma', {}).get('similitud_histograma', 0.0)
            stats_val = analisis.get('similitud_estadistica', {}).get('similitud_estadistica', 0.0)
            struct_val = analisis.get('similitud_estructura', {}).get('similitud_estructura', 0.0)
            
            # Aplicar pesos
            pesos = self.config['pesos_metricas']
            fidelidad = (
                pixel_val * pesos['similitud_pixel'] +
                hist_val * pesos['similitud_histograma'] +
                stats_val * pesos['similitud_estadistica'] +
                struct_val * pesos['similitud_estructura']
            )
            
            return max(0.0, min(1.0, fidelidad))
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando fidelidad total: {e}")
            return 0.0
    
    def _generar_resumen_augment(self, analisis: Dict) -> Dict:
        """Generar resumen para Augment IA."""
        try:
            fidelidad = analisis.get('fidelidad_total', 0.0)
            
            resumen = {
                'fidelidad_total': fidelidad,
                'metodo_analisis': 'LIBRERIAS_DISPONIBLES',
                'estado': 'EXCELENTE' if fidelidad >= 0.98 else 'BUENO' if fidelidad >= 0.85 else 'REQUIERE_MEJORA',
                'objetivo_98_alcanzado': fidelidad >= 0.98,
                'confianza_analisis': 0.85,
                
                'metricas_principales': {
                    'similitud_pixel': analisis.get('similitud_pixel', {}).get('similitud_pixel', 0.0),
                    'similitud_histograma': analisis.get('similitud_histograma', {}).get('similitud_histograma', 0.0),
                    'similitud_estadistica': analisis.get('similitud_estadistica', {}).get('similitud_estadistica', 0.0)
                },
                
                'areas_criticas': [],
                'fortalezas': [],
                'recomendaciones_mejora': []
            }
            
            # Identificar áreas críticas
            if analisis.get('similitud_pixel', {}).get('similitud_pixel', 0.0) < 0.8:
                resumen['areas_criticas'].append('Diferencias significativas a nivel de píxel')
            
            if analisis.get('similitud_histograma', {}).get('similitud_histograma', 0.0) < 0.7:
                resumen['areas_criticas'].append('Distribución de intensidades requiere ajuste')
            
            # Identificar fortalezas
            if analisis.get('similitud_pixel', {}).get('similitud_pixel', 0.0) > 0.9:
                resumen['fortalezas'].append('Excelente similitud a nivel de píxel')
            
            # Generar recomendaciones
            if fidelidad < 0.98:
                resumen['recomendaciones_mejora'] = [
                    'Ajustar coordenadas y proporciones principales',
                    'Corregir elementos geométricos básicos',
                    'Optimizar distribución de elementos'
                ]
            
            return resumen
            
        except Exception as e:
            self.logger.error(f"❌ Error generando resumen: {e}")
            return {'error': str(e)}

def test_analizador_disponible():
    """Test del analizador con librerías disponibles."""
    print("🧪 Probando analizador con librerías disponibles...")
    
    try:
        analizador = AnalizadorDisponible()
        
        # Crear imagen de prueba
        import tempfile
        
        # Crear imagen simple con Pillow
        img_test = Image.new('RGB', (100, 100), color='white')
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            img_test.save(f.name)
            
            # Test de análisis
            resultado = analizador.analizar_fidelidad_completa(f.name, f.name)
            
            print(f"✅ Fidelidad (imagen idéntica): {resultado.get('fidelidad_total', 0.0):.2%}")
            print("🎯 Analizador con librerías disponibles funcionando")
            
            # Limpiar
            Path(f.name).unlink()
        
    except Exception as e:
        print(f"❌ Error en test: {e}")

if __name__ == "__main__":
    test_analizador_disponible()
