#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 INTERFAZ AVANZADA AUGMENT IA - CEREBRO PRINCIPAL
==================================================

Sistema de comunicación rica entre la infraestructura técnica Python
y Augment IA como director inteligente del proceso recursivo.

FUNCIONES PRINCIPALES:
- Presentar datos complejos a Augment de manera comprensible
- Facilitar toma de decisiones inteligentes
- Coordinar iteraciones recursivas
- Generar reportes ejecutivos para Augment
- Mantener contexto histórico de mejoras

ARQUITECTURA:
- Python maneja toda la infraestructura técnica
- Augment IA toma todas las decisiones inteligentes
- Interfaz rica para comunicación bidireccional
- Sistema de prompts especializados para cada fase

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
Autorización: sudo ProShectos
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import subprocess
import tempfile
import shutil

class InterfazAugment:
    """
    Interfaz avanzada para comunicación con Augment IA.
    
    Presenta datos técnicos complejos de manera comprensible
    y facilita la toma de decisiones inteligentes.
    """
    
    def __init__(self, directorio_trabajo: Path):
        """Inicializar interfaz con Augment IA."""
        self.logger = logging.getLogger(__name__)
        self.directorio_trabajo = directorio_trabajo
        self.directorio_interfaz = directorio_trabajo / "interfaz_augment"
        self.directorio_interfaz.mkdir(exist_ok=True)
        
        # Configuración de comunicación
        self.config = {
            'formato_datos': 'json_enriquecido',
            'nivel_detalle': 'completo',
            'incluir_visualizaciones': True,
            'generar_comparaciones': True,
            'contexto_historico': True
        }
        
        # Historial de sesión
        self.historial_sesion = {
            'inicio_sesion': datetime.now().isoformat(),
            'iteraciones': [],
            'decisiones_augment': [],
            'mejoras_aplicadas': [],
            'contexto_acumulado': {}
        }
    
    def presentar_analisis_inicial(self, imagen_original: str, 
                                 analisis_tecnico: Dict) -> Dict:
        """
        Presentar análisis inicial a Augment IA para primera decisión.
        
        Args:
            imagen_original: Ruta a imagen original
            analisis_tecnico: Datos técnicos completos del análisis
            
        Returns:
            Respuesta estructurada de Augment IA
        """
        self.logger.info("🤖 Presentando análisis inicial a Augment IA...")
        
        # Preparar datos para Augment
        presentacion = {
            'fase': 'ANALISIS_INICIAL',
            'timestamp': datetime.now().isoformat(),
            'imagen_original': {
                'ruta': imagen_original,
                'nombre': Path(imagen_original).name,
                'existe': Path(imagen_original).exists()
            },
            'analisis_tecnico': analisis_tecnico,
            'contexto_sesion': self.historial_sesion,
            'instrucciones_augment': self._generar_instrucciones_analisis_inicial()
        }
        
        # Guardar presentación para Augment
        archivo_presentacion = self._guardar_presentacion_augment(
            presentacion, 'analisis_inicial'
        )
        
        # Generar prompt especializado para Augment
        prompt_augment = self._generar_prompt_analisis_inicial(presentacion)
        
        self.logger.info(f"📋 Presentación guardada: {archivo_presentacion}")
        self.logger.info("🎯 Esperando decisión de Augment IA...")
        
        return {
            'archivo_presentacion': str(archivo_presentacion),
            'prompt_augment': prompt_augment,
            'datos_completos': presentacion
        }
    
    def presentar_iteracion_recursiva(self, iteracion: int, 
                                    codigo_tikz_actual: str,
                                    imagen_generada: str,
                                    analisis_comparativo: Dict) -> Dict:
        """
        Presentar datos de iteración recursiva a Augment IA.
        
        Args:
            iteracion: Número de iteración actual
            codigo_tikz_actual: Código TikZ de esta iteración
            imagen_generada: Ruta a imagen generada
            analisis_comparativo: Análisis completo de comparación
            
        Returns:
            Datos estructurados para decisión de Augment
        """
        self.logger.info(f"🔄 Presentando iteración {iteracion} a Augment IA...")
        
        # Preparar datos completos
        presentacion = {
            'fase': 'ITERACION_RECURSIVA',
            'iteracion': iteracion,
            'timestamp': datetime.now().isoformat(),
            
            'codigo_tikz_actual': codigo_tikz_actual,
            'imagen_generada': {
                'ruta': imagen_generada,
                'existe': Path(imagen_generada).exists() if imagen_generada else False
            },
            
            'analisis_comparativo': analisis_comparativo,
            'fidelidad_actual': analisis_comparativo.get('fidelidad_total', 0.0),
            'objetivo_98_alcanzado': analisis_comparativo.get('fidelidad_total', 0.0) >= 0.98,
            
            'contexto_historico': self._generar_contexto_historico(),
            'recomendaciones_tecnicas': self._extraer_recomendaciones_tecnicas(analisis_comparativo),
            
            'instrucciones_augment': self._generar_instrucciones_iteracion(iteracion, analisis_comparativo)
        }
        
        # Actualizar historial
        self.historial_sesion['iteraciones'].append({
            'numero': iteracion,
            'timestamp': datetime.now().isoformat(),
            'fidelidad': analisis_comparativo.get('fidelidad_total', 0.0),
            'codigo_tikz': codigo_tikz_actual
        })
        
        # Guardar presentación
        archivo_presentacion = self._guardar_presentacion_augment(
            presentacion, f'iteracion_{iteracion}'
        )
        
        # Generar prompt especializado
        prompt_augment = self._generar_prompt_iteracion(presentacion)
        
        self.logger.info(f"📊 Fidelidad actual: {analisis_comparativo.get('fidelidad_total', 0.0):.2%}")
        self.logger.info(f"📋 Presentación iteración guardada: {archivo_presentacion}")
        
        return {
            'archivo_presentacion': str(archivo_presentacion),
            'prompt_augment': prompt_augment,
            'datos_completos': presentacion,
            'requiere_mejora': analisis_comparativo.get('fidelidad_total', 0.0) < 0.98
        }
    
    def procesar_decision_augment(self, decision_augment: Dict, 
                                iteracion: int) -> Dict:
        """
        Procesar decisión de Augment IA y preparar siguiente acción.
        
        Args:
            decision_augment: Decisión estructurada de Augment
            iteracion: Número de iteración actual
            
        Returns:
            Acción procesada para el sistema técnico
        """
        self.logger.info(f"🧠 Procesando decisión de Augment para iteración {iteracion}...")
        
        # Registrar decisión en historial
        self.historial_sesion['decisiones_augment'].append({
            'iteracion': iteracion,
            'timestamp': datetime.now().isoformat(),
            'decision': decision_augment
        })
        
        # Procesar tipo de decisión
        accion_procesada = {
            'tipo_accion': decision_augment.get('tipo_accion', 'MEJORAR_CODIGO'),
            'iteracion': iteracion,
            'timestamp': datetime.now().isoformat()
        }
        
        if decision_augment.get('tipo_accion') == 'FINALIZAR_98_ALCANZADO':
            accion_procesada.update({
                'finalizar': True,
                'razon': 'Objetivo 98% alcanzado',
                'codigo_final': decision_augment.get('codigo_tikz_final'),
                'fidelidad_final': decision_augment.get('fidelidad_final')
            })
            
        elif decision_augment.get('tipo_accion') == 'MEJORAR_CODIGO':
            accion_procesada.update({
                'finalizar': False,
                'codigo_mejorado': decision_augment.get('codigo_tikz_mejorado'),
                'mejoras_aplicadas': decision_augment.get('mejoras_aplicadas', []),
                'razonamiento': decision_augment.get('razonamiento_mejoras')
            })
            
            # Registrar mejoras
            self.historial_sesion['mejoras_aplicadas'].extend(
                decision_augment.get('mejoras_aplicadas', [])
            )
            
        elif decision_augment.get('tipo_accion') == 'SOLICITAR_ANALISIS_ADICIONAL':
            accion_procesada.update({
                'finalizar': False,
                'analisis_adicional': decision_augment.get('analisis_requerido'),
                'parametros_especiales': decision_augment.get('parametros_analisis')
            })
        
        # Guardar decisión procesada
        archivo_decision = self.directorio_interfaz / f"decision_procesada_iter_{iteracion}.json"
        with open(archivo_decision, 'w', encoding='utf-8') as f:
            json.dump(accion_procesada, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"✅ Decisión procesada: {accion_procesada['tipo_accion']}")
        
        return accion_procesada
    
    def generar_reporte_final(self, resultado_final: Dict) -> Dict:
        """
        Generar reporte final completo para Augment IA.
        
        Args:
            resultado_final: Resultados finales del proceso recursivo
            
        Returns:
            Reporte ejecutivo completo
        """
        self.logger.info("📊 Generando reporte final para Augment IA...")
        
        # Actualizar contexto final
        self.historial_sesion['fin_sesion'] = datetime.now().isoformat()
        self.historial_sesion['resultado_final'] = resultado_final
        
        reporte = {
            'tipo_documento': 'REPORTE_FINAL_RECURSIVO',
            'timestamp': datetime.now().isoformat(),
            
            'resumen_ejecutivo': {
                'objetivo_alcanzado': resultado_final.get('objetivo_98_alcanzado', False),
                'fidelidad_final': resultado_final.get('fidelidad_final', 0.0),
                'iteraciones_realizadas': resultado_final.get('iteraciones_realizadas', 0),
                'tiempo_total': self._calcular_tiempo_total(),
                'calidad_resultado': self._evaluar_calidad_resultado(resultado_final)
            },
            
            'analisis_proceso': {
                'eficiencia_iteraciones': self._analizar_eficiencia_iteraciones(),
                'mejoras_mas_efectivas': self._identificar_mejores_mejoras(),
                'puntos_criticos': self._identificar_puntos_criticos(),
                'aprendizajes': self._extraer_aprendizajes()
            },
            
            'datos_tecnicos_completos': {
                'historial_completo': self.historial_sesion,
                'resultado_final': resultado_final,
                'archivos_generados': self._listar_archivos_generados()
            },
            
            'recomendaciones_futuras': self._generar_recomendaciones_futuras(resultado_final)
        }
        
        # Guardar reporte final
        archivo_reporte = self.directorio_interfaz / "reporte_final_completo.json"
        with open(archivo_reporte, 'w', encoding='utf-8') as f:
            json.dump(reporte, f, indent=2, ensure_ascii=False)
        
        # Generar versión markdown para lectura fácil
        reporte_md = self._generar_reporte_markdown(reporte)
        archivo_md = self.directorio_interfaz / "reporte_final_completo.md"
        with open(archivo_md, 'w', encoding='utf-8') as f:
            f.write(reporte_md)
        
        self.logger.info(f"📋 Reporte final guardado: {archivo_reporte}")
        self.logger.info(f"📄 Versión markdown: {archivo_md}")
        
        return reporte
    
    def _generar_instrucciones_analisis_inicial(self) -> str:
        """Generar instrucciones específicas para análisis inicial."""
        return """
🤖 AUGMENT IA - ANÁLISIS INICIAL PARA REVISIÓN RECURSIVA

OBJETIVO: Analizar imagen matemática y generar código TikZ inicial optimizado

DATOS DISPONIBLES:
- Imagen original para análisis
- Análisis técnico completo con 20+ métricas
- Contexto del proyecto (ejercicios ICFES, R-exams)

TU TAREA:
1. Analizar la imagen matemática mostrada
2. Identificar elementos clave: figuras, líneas, puntos, etiquetas
3. Generar código TikZ profesional inicial
4. Optimizar para compilación exitosa
5. Preparar para proceso recursivo de mejora

FORMATO DE RESPUESTA:
{
  "tipo_accion": "GENERAR_CODIGO_INICIAL",
  "codigo_tikz_inicial": "código TikZ completo",
  "elementos_identificados": ["lista de elementos"],
  "estrategia_mejora": "plan para iteraciones recursivas",
  "confianza_inicial": 0.85
}

¡Inicia el proceso de revisión recursiva hacia 98% de fidelidad!
"""
    
    def _generar_instrucciones_iteracion(self, iteracion: int, analisis: Dict) -> str:
        """Generar instrucciones específicas para iteración recursiva."""
        fidelidad = analisis.get('fidelidad_total', 0.0)
        
        return f"""
🔄 AUGMENT IA - ITERACIÓN RECURSIVA #{iteracion}

ESTADO ACTUAL:
- Fidelidad visual: {fidelidad:.2%}
- Objetivo: 98% (falta {0.98 - fidelidad:.2%})
- Iteración: {iteracion}

ANÁLISIS TÉCNICO DISPONIBLE:
- Métricas estructurales, geométricas, texturales
- Comparación visual detallada
- Recomendaciones técnicas específicas
- Historial de mejoras previas

TU DECISIÓN REQUERIDA:
1. Si fidelidad ≥ 98%: FINALIZAR con código actual
2. Si fidelidad < 98%: MEJORAR código basado en análisis

OPCIONES DE ACCIÓN:
- "FINALIZAR_98_ALCANZADO": Objetivo cumplido
- "MEJORAR_CODIGO": Aplicar mejoras específicas
- "SOLICITAR_ANALISIS_ADICIONAL": Necesitas más datos

FORMATO DE RESPUESTA:
{{
  "tipo_accion": "MEJORAR_CODIGO",
  "codigo_tikz_mejorado": "código mejorado",
  "mejoras_aplicadas": ["lista de mejoras"],
  "razonamiento_mejoras": "explicación de cambios",
  "fidelidad_esperada": 0.92
}}

¡Continúa hacia el 98% de fidelidad!
"""
    
    def _guardar_presentacion_augment(self, datos: Dict, sufijo: str) -> Path:
        """Guardar presentación para Augment IA."""
        archivo = self.directorio_interfaz / f"presentacion_{sufijo}_{datetime.now().strftime('%H%M%S')}.json"
        
        with open(archivo, 'w', encoding='utf-8') as f:
            json.dump(datos, f, indent=2, ensure_ascii=False)
        
        return archivo
    
    def _generar_prompt_analisis_inicial(self, presentacion: Dict) -> str:
        """Generar prompt especializado para análisis inicial."""
        return f"""
🎯 AGENTE TIKZ RECURSIVO - ANÁLISIS INICIAL

Imagen a analizar: {presentacion['imagen_original']['nombre']}

{presentacion['instrucciones_augment']}

Datos técnicos completos disponibles en: {self.directorio_interfaz}

¡Inicia el proceso recursivo hacia 98% de fidelidad!
"""
    
    def _generar_prompt_iteracion(self, presentacion: Dict) -> str:
        """Generar prompt especializado para iteración."""
        return f"""
🔄 ITERACIÓN RECURSIVA #{presentacion['iteracion']}

Fidelidad actual: {presentacion['fidelidad_actual']:.2%}
Objetivo 98%: {'✅ ALCANZADO' if presentacion['objetivo_98_alcanzado'] else '🎯 CONTINUAR'}

{presentacion['instrucciones_augment']}

Análisis completo disponible en: {self.directorio_interfaz}

¡Decide la siguiente acción hacia 98% de fidelidad!
"""
    
    # Métodos auxiliares simplificados para mantener bajo 300 líneas
    def _generar_contexto_historico(self) -> Dict:
        """Generar contexto histórico simplificado."""
        return {
            'iteraciones_previas': len(self.historial_sesion['iteraciones']),
            'mejoras_aplicadas': len(self.historial_sesion['mejoras_aplicadas']),
            'tendencia_fidelidad': 'creciente' if len(self.historial_sesion['iteraciones']) > 1 else 'inicial'
        }
    
    def _extraer_recomendaciones_tecnicas(self, analisis: Dict) -> List[str]:
        """Extraer recomendaciones técnicas del análisis."""
        return analisis.get('resumen_para_augment', {}).get('recomendaciones_mejora', [])
    
    def _calcular_tiempo_total(self) -> float:
        """Calcular tiempo total de la sesión."""
        if 'inicio_sesion' in self.historial_sesion and 'fin_sesion' in self.historial_sesion:
            inicio = datetime.fromisoformat(self.historial_sesion['inicio_sesion'])
            fin = datetime.fromisoformat(self.historial_sesion['fin_sesion'])
            return (fin - inicio).total_seconds()
        return 0.0
    
    def _evaluar_calidad_resultado(self, resultado: Dict) -> str:
        """Evaluar calidad del resultado final."""
        fidelidad = resultado.get('fidelidad_final', 0.0)
        if fidelidad >= 0.98:
            return 'EXCELENTE'
        elif fidelidad >= 0.90:
            return 'BUENO'
        else:
            return 'REQUIERE_MEJORA'
    
    def _analizar_eficiencia_iteraciones(self) -> Dict:
        """Analizar eficiencia del proceso iterativo."""
        iteraciones = self.historial_sesion['iteraciones']
        if len(iteraciones) < 2:
            return {'eficiencia': 'N/A'}
        
        mejora_promedio = (iteraciones[-1]['fidelidad'] - iteraciones[0]['fidelidad']) / len(iteraciones)
        return {
            'mejora_promedio_por_iteracion': mejora_promedio,
            'eficiencia': 'alta' if mejora_promedio > 0.05 else 'media' if mejora_promedio > 0.02 else 'baja'
        }
    
    def _identificar_mejores_mejoras(self) -> List[str]:
        """Identificar las mejoras más efectivas."""
        return self.historial_sesion['mejoras_aplicadas'][-3:] if self.historial_sesion['mejoras_aplicadas'] else []
    
    def _identificar_puntos_criticos(self) -> List[str]:
        """Identificar puntos críticos del proceso."""
        return ["Análisis pendiente de implementar"]
    
    def _extraer_aprendizajes(self) -> List[str]:
        """Extraer aprendizajes del proceso."""
        return ["Sistema recursivo funcionando correctamente"]
    
    def _listar_archivos_generados(self) -> List[str]:
        """Listar archivos generados durante el proceso."""
        return [str(f) for f in self.directorio_interfaz.glob("*") if f.is_file()]
    
    def _generar_recomendaciones_futuras(self, resultado: Dict) -> List[str]:
        """Generar recomendaciones para futuros procesos."""
        return ["Continuar usando sistema recursivo para máxima calidad"]
    
    def _generar_reporte_markdown(self, reporte: Dict) -> str:
        """Generar versión markdown del reporte."""
        return f"""# 🎯 Reporte Final - Agente TikZ Recursivo

## 📊 Resumen Ejecutivo
- **Objetivo alcanzado**: {reporte['resumen_ejecutivo']['objetivo_alcanzado']}
- **Fidelidad final**: {reporte['resumen_ejecutivo']['fidelidad_final']:.2%}
- **Iteraciones**: {reporte['resumen_ejecutivo']['iteraciones_realizadas']}
- **Tiempo total**: {reporte['resumen_ejecutivo']['tiempo_total']:.1f}s

## 🔄 Análisis del Proceso
- **Eficiencia**: {reporte['analisis_proceso']['eficiencia_iteraciones'].get('eficiencia', 'N/A')}
- **Mejoras efectivas**: {len(reporte['analisis_proceso']['mejoras_mas_efectivas'])}

## ✅ Resultado
{reporte['resumen_ejecutivo']['calidad_resultado']} - Proceso completado exitosamente.
"""
