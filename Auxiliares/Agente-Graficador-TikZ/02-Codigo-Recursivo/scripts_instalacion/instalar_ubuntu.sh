#!/bin/bash
# Script de instalación para Ubuntu/Debian
echo "🐧 Instalando dependencias para Agente TikZ en Ubuntu/Debian..."

# Actualizar sistema
sudo apt update

# Instalar Python y pip si no están instalados
sudo apt install -y python3 python3-pip

# Instalar dependencias del sistema para OpenCV
sudo apt install -y python3-opencv

# Instalar librerías Python globalmente
pip3 install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

echo "✅ Instalación completada para Ubuntu/Debian"
