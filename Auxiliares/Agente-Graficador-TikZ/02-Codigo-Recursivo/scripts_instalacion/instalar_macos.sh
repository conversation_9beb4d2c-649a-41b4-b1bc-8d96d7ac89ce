#!/bin/bash
# Script de instalación para macOS
echo "🍎 Instalando dependencias para Agente TikZ en macOS..."

# Verificar que Homebrew esté instalado
if ! command -v brew &> /dev/null; then
    echo "❌ Homebrew no encontrado. Instala desde https://brew.sh/"
    exit 1
fi

# Instalar Python si no está instalado
brew install python

# Instalar librerías Python globalmente
pip3 install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

echo "✅ Instalación completada para macOS"
