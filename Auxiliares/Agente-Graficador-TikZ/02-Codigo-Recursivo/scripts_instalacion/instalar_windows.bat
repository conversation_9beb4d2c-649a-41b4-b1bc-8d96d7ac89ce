@echo off
REM Script de instalación para Windows
echo 🪟 Instalando dependencias para Agente TikZ en Windows...

REM Verificar que Python esté instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python no encontrado. Instala desde https://python.org/
    pause
    exit /b 1
)

REM Instalar librerías Python globalmente
pip install --upgrade opencv-python numpy pillow scikit-image scipy matplotlib

echo ✅ Instalación completada para Windows
pause
