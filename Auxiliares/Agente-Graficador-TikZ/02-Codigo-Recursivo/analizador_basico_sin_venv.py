#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 ANALIZADOR VISUAL BÁSICO SIN ENTORNOS VIRTUALES
==================================================

Analizador visual robusto que funciona SOLO con librerías estándar de Python
y OpenCV (disponible globalmente). NO requiere entornos virtuales.

CARACTERÍSTICAS:
- Solo usa librerías estándar: cv2, numpy, PIL
- Implementaciones propias de algoritmos avanzados
- Métricas robustas sin dependencias complejas
- Optimizado para Augment IA como cerebro principal
- Funciona en cualquier sistema con Python básico

MÉTRICAS IMPLEMENTADAS:
- SSIM básico (implementación propia)
- MSE y PSNR
- Análisis de contornos con OpenCV
- Comparación de histogramas
- Análisis geométrico básico
- Métricas de similitud estructural

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
Autorización: sudo ProShectos
"""

import cv2
import numpy as np
from PIL import Image, ImageStat, ImageFilter
import logging
import json
import math
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any

class AnalizadorBasicoSinVenv:
    """
    Analizador visual robusto que funciona sin entornos virtuales.
    
    Usa solo librerías estándar disponibles globalmente:
    - OpenCV (cv2)
    - NumPy 
    - PIL/Pillow
    - Librerías estándar de Python
    """
    
    def __init__(self):
        """Inicializar analizador básico."""
        self.logger = logging.getLogger(__name__)
        
        # Configuración optimizada
        self.config = {
            'resolucion_analisis': (800, 600),
            'umbral_contorno': 50,
            'sigma_gaussian': 1.0,
            
            # Pesos para métricas combinadas
            'pesos_metricas': {
                'ssim_basico': 0.30,
                'mse_normalizado': 0.25,
                'similitud_contornos': 0.20,
                'similitud_histogramas': 0.15,
                'similitud_geometrica': 0.10
            }
        }
        
        # Verificar disponibilidad de OpenCV
        self._verificar_opencv()
    
    def _verificar_opencv(self):
        """Verificar que OpenCV esté disponible."""
        try:
            # Test básico de OpenCV
            test_img = np.zeros((100, 100, 3), dtype=np.uint8)
            cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
            self.logger.info("✅ OpenCV disponible globalmente")
        except Exception as e:
            self.logger.error(f"❌ OpenCV no disponible: {e}")
            raise Exception("OpenCV requerido para funcionamiento básico")
    
    def analizar_fidelidad_completa(self, imagen_original: str, 
                                  imagen_generada: str) -> Dict[str, Any]:
        """
        Análisis completo usando solo librerías estándar.
        
        Args:
            imagen_original: Ruta a imagen original
            imagen_generada: Ruta a imagen generada
            
        Returns:
            Dict completo con métricas para Augment IA
        """
        self.logger.info("📊 Iniciando análisis básico sin entornos virtuales...")
        
        try:
            # Cargar imágenes
            img_orig = self._cargar_imagen_estandar(imagen_original)
            img_gen = self._cargar_imagen_estandar(imagen_generada)
            
            if img_orig is None or img_gen is None:
                raise Exception("Error cargando imágenes")
            
            # Realizar análisis completo
            analisis = {
                'timestamp': datetime.now().isoformat(),
                'metodo_analisis': 'BASICO_SIN_VENV',
                'imagenes': {
                    'original': imagen_original,
                    'generada': imagen_generada
                },
                
                # Métricas básicas robustas
                'ssim_basico': self._calcular_ssim_basico(img_orig, img_gen),
                'mse_y_psnr': self._calcular_mse_psnr(img_orig, img_gen),
                'similitud_contornos': self._analizar_contornos_opencv(img_orig, img_gen),
                'similitud_histogramas': self._comparar_histogramas(img_orig, img_gen),
                'similitud_geometrica': self._analizar_geometria_basica(img_orig, img_gen),
                
                # Métricas combinadas
                'fidelidad_total': 0.0,
                'confianza_analisis': 0.95,  # Alta confianza en métodos básicos
                
                # Resumen para Augment IA
                'resumen_para_augment': {}
            }
            
            # Calcular fidelidad total
            analisis['fidelidad_total'] = self._calcular_fidelidad_total_basica(analisis)
            
            # Generar resumen para Augment
            analisis['resumen_para_augment'] = self._generar_resumen_augment_basico(analisis)
            
            self.logger.info(f"✅ Análisis básico completado: {analisis['fidelidad_total']:.2%}")
            
            return analisis
            
        except Exception as e:
            self.logger.error(f"❌ Error en análisis básico: {e}")
            return {
                'error': str(e),
                'fidelidad_total': 0.0,
                'resumen_para_augment': {
                    'estado': 'ERROR',
                    'mensaje': f"Error en análisis: {e}"
                }
            }
    
    def _cargar_imagen_estandar(self, ruta_imagen: str) -> Optional[np.ndarray]:
        """Cargar imagen usando solo OpenCV estándar."""
        try:
            if not Path(ruta_imagen).exists():
                self.logger.error(f"❌ Imagen no encontrada: {ruta_imagen}")
                return None
            
            # Cargar con OpenCV
            imagen = cv2.imread(str(ruta_imagen))
            if imagen is None:
                return None
            
            # Convertir a RGB
            imagen_rgb = cv2.cvtColor(imagen, cv2.COLOR_BGR2RGB)
            
            # Redimensionar
            imagen_resized = cv2.resize(
                imagen_rgb, 
                self.config['resolucion_analisis'], 
                interpolation=cv2.INTER_LANCZOS4
            )
            
            # Convertir a escala de grises para análisis
            imagen_gray = cv2.cvtColor(imagen_resized, cv2.COLOR_RGB2GRAY)
            
            # Normalizar
            return imagen_gray.astype(np.float64) / 255.0
            
        except Exception as e:
            self.logger.error(f"❌ Error cargando imagen: {e}")
            return None
    
    def _calcular_ssim_basico(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Implementación básica de SSIM sin librerías externas."""
        try:
            # Parámetros SSIM
            K1, K2 = 0.01, 0.03
            L = 1.0  # Rango dinámico
            
            # Constantes
            C1 = (K1 * L) ** 2
            C2 = (K2 * L) ** 2
            
            # Medias
            mu1 = cv2.GaussianBlur(img1, (11, 11), 1.5)
            mu2 = cv2.GaussianBlur(img2, (11, 11), 1.5)
            
            mu1_sq = mu1 ** 2
            mu2_sq = mu2 ** 2
            mu1_mu2 = mu1 * mu2
            
            # Varianzas y covarianza
            sigma1_sq = cv2.GaussianBlur(img1 ** 2, (11, 11), 1.5) - mu1_sq
            sigma2_sq = cv2.GaussianBlur(img2 ** 2, (11, 11), 1.5) - mu2_sq
            sigma12 = cv2.GaussianBlur(img1 * img2, (11, 11), 1.5) - mu1_mu2
            
            # SSIM
            numerador = (2 * mu1_mu2 + C1) * (2 * sigma12 + C2)
            denominador = (mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2)
            
            ssim_map = numerador / denominador
            ssim_value = float(np.mean(ssim_map))
            
            return {
                'ssim_global': ssim_value,
                'ssim_promedio': ssim_value,
                'ssim_std': float(np.std(ssim_map)),
                'ssim_min': float(np.min(ssim_map)),
                'ssim_max': float(np.max(ssim_map))
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando SSIM básico: {e}")
            return {'ssim_global': 0.0}
    
    def _calcular_mse_psnr(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Calcular MSE y PSNR básicos."""
        try:
            # MSE
            mse = np.mean((img1 - img2) ** 2)
            
            # PSNR
            if mse > 0:
                psnr = 20 * math.log10(1.0 / math.sqrt(mse))
            else:
                psnr = float('inf')
            
            # MSE normalizado para similitud
            mse_normalizado = 1.0 - min(mse, 1.0)
            
            return {
                'mse': float(mse),
                'psnr': float(psnr),
                'mse_normalizado': float(mse_normalizado),
                'similitud_mse': float(mse_normalizado)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando MSE/PSNR: {e}")
            return {'mse': 1.0, 'psnr': 0.0, 'mse_normalizado': 0.0}
    
    def _analizar_contornos_opencv(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis de contornos usando OpenCV estándar."""
        try:
            # Convertir a uint8 para OpenCV
            img1_uint8 = (img1 * 255).astype(np.uint8)
            img2_uint8 = (img2 * 255).astype(np.uint8)
            
            # Detectar contornos con Canny
            contornos1 = cv2.Canny(img1_uint8, 50, 150)
            contornos2 = cv2.Canny(img2_uint8, 50, 150)
            
            # Normalizar a [0, 1]
            contornos1 = contornos1.astype(bool)
            contornos2 = contornos2.astype(bool)
            
            # Métricas de contornos
            interseccion = np.logical_and(contornos1, contornos2)
            union = np.logical_or(contornos1, contornos2)
            
            if np.sum(union) > 0:
                jaccard = float(np.sum(interseccion) / np.sum(union))
            else:
                jaccard = 1.0
            
            # Densidad de contornos
            densidad1 = float(np.sum(contornos1) / contornos1.size)
            densidad2 = float(np.sum(contornos2) / contornos2.size)
            
            return {
                'jaccard_contornos': jaccard,
                'densidad_contornos_1': densidad1,
                'densidad_contornos_2': densidad2,
                'diferencia_densidad': abs(densidad1 - densidad2),
                'similitud_contornos': jaccard
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error analizando contornos: {e}")
            return {'similitud_contornos': 0.0}
    
    def _comparar_histogramas(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Comparar histogramas de intensidad."""
        try:
            # Calcular histogramas
            hist1 = cv2.calcHist([img1], [0], None, [256], [0, 1])
            hist2 = cv2.calcHist([img2], [0], None, [256], [0, 1])
            
            # Normalizar
            hist1 = hist1.flatten() / np.sum(hist1)
            hist2 = hist2.flatten() / np.sum(hist2)
            
            # Correlación de histogramas
            correlacion = float(cv2.compareHist(
                hist1.astype(np.float32), 
                hist2.astype(np.float32), 
                cv2.HISTCMP_CORREL
            ))
            
            # Chi-cuadrado
            chi_cuadrado = float(cv2.compareHist(
                hist1.astype(np.float32), 
                hist2.astype(np.float32), 
                cv2.HISTCMP_CHISQR
            ))
            
            # Intersección
            interseccion = float(cv2.compareHist(
                hist1.astype(np.float32), 
                hist2.astype(np.float32), 
                cv2.HISTCMP_INTERSECT
            ))
            
            return {
                'correlacion_histograma': correlacion,
                'chi_cuadrado': chi_cuadrado,
                'interseccion_histograma': interseccion,
                'similitud_histograma': correlacion
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error comparando histogramas: {e}")
            return {'similitud_histograma': 0.0}
    
    def _analizar_geometria_basica(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """Análisis geométrico básico usando OpenCV."""
        try:
            # Convertir a uint8
            img1_uint8 = (img1 * 255).astype(np.uint8)
            img2_uint8 = (img2 * 255).astype(np.uint8)
            
            # Detectar esquinas con Harris
            esquinas1 = cv2.cornerHarris(img1_uint8, 2, 3, 0.04)
            esquinas2 = cv2.cornerHarris(img2_uint8, 2, 3, 0.04)
            
            # Contar esquinas significativas
            umbral = 0.01 * esquinas1.max()
            num_esquinas1 = np.sum(esquinas1 > umbral)
            num_esquinas2 = np.sum(esquinas2 > umbral)
            
            # Detectar líneas con HoughLines
            contornos1 = cv2.Canny(img1_uint8, 50, 150)
            contornos2 = cv2.Canny(img2_uint8, 50, 150)
            
            lineas1 = cv2.HoughLines(contornos1, 1, np.pi/180, threshold=50)
            lineas2 = cv2.HoughLines(contornos2, 1, np.pi/180, threshold=50)
            
            num_lineas1 = len(lineas1) if lineas1 is not None else 0
            num_lineas2 = len(lineas2) if lineas2 is not None else 0
            
            # Calcular similitud geométrica
            if max(num_esquinas1, num_esquinas2) > 0:
                similitud_esquinas = 1.0 - abs(num_esquinas1 - num_esquinas2) / max(num_esquinas1, num_esquinas2)
            else:
                similitud_esquinas = 1.0
            
            if max(num_lineas1, num_lineas2) > 0:
                similitud_lineas = 1.0 - abs(num_lineas1 - num_lineas2) / max(num_lineas1, num_lineas2)
            else:
                similitud_lineas = 1.0
            
            similitud_geometrica = (similitud_esquinas + similitud_lineas) / 2
            
            return {
                'num_esquinas_1': int(num_esquinas1),
                'num_esquinas_2': int(num_esquinas2),
                'num_lineas_1': int(num_lineas1),
                'num_lineas_2': int(num_lineas2),
                'similitud_esquinas': float(similitud_esquinas),
                'similitud_lineas': float(similitud_lineas),
                'similitud_geometrica': float(similitud_geometrica)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error en análisis geométrico: {e}")
            return {'similitud_geometrica': 0.0}
    
    def _calcular_fidelidad_total_basica(self, analisis: Dict) -> float:
        """Calcular fidelidad total usando métricas básicas."""
        try:
            # Extraer métricas principales
            ssim_val = analisis.get('ssim_basico', {}).get('ssim_global', 0.0)
            mse_val = analisis.get('mse_y_psnr', {}).get('mse_normalizado', 0.0)
            contornos_val = analisis.get('similitud_contornos', {}).get('similitud_contornos', 0.0)
            hist_val = analisis.get('similitud_histogramas', {}).get('similitud_histograma', 0.0)
            geom_val = analisis.get('similitud_geometrica', {}).get('similitud_geometrica', 0.0)
            
            # Aplicar pesos
            pesos = self.config['pesos_metricas']
            fidelidad = (
                ssim_val * pesos['ssim_basico'] +
                mse_val * pesos['mse_normalizado'] +
                contornos_val * pesos['similitud_contornos'] +
                hist_val * pesos['similitud_histogramas'] +
                geom_val * pesos['similitud_geometrica']
            )
            
            return max(0.0, min(1.0, fidelidad))
            
        except Exception as e:
            self.logger.error(f"❌ Error calculando fidelidad total: {e}")
            return 0.0
    
    def _generar_resumen_augment_basico(self, analisis: Dict) -> Dict:
        """Generar resumen para Augment IA usando análisis básico."""
        try:
            fidelidad = analisis.get('fidelidad_total', 0.0)
            
            resumen = {
                'fidelidad_total': fidelidad,
                'metodo_analisis': 'BASICO_SIN_VENV',
                'estado': 'EXCELENTE' if fidelidad >= 0.98 else 'BUENO' if fidelidad >= 0.85 else 'REQUIERE_MEJORA',
                'objetivo_98_alcanzado': fidelidad >= 0.98,
                'confianza_analisis': 0.95,
                
                'metricas_principales': {
                    'ssim_basico': analisis.get('ssim_basico', {}).get('ssim_global', 0.0),
                    'similitud_contornos': analisis.get('similitud_contornos', {}).get('similitud_contornos', 0.0),
                    'similitud_histogramas': analisis.get('similitud_histogramas', {}).get('similitud_histograma', 0.0)
                },
                
                'areas_criticas': [],
                'fortalezas': [],
                'recomendaciones_mejora': []
            }
            
            # Identificar áreas críticas
            if analisis.get('ssim_basico', {}).get('ssim_global', 0.0) < 0.8:
                resumen['areas_criticas'].append('Estructura general requiere ajustes')
            
            if analisis.get('similitud_contornos', {}).get('similitud_contornos', 0.0) < 0.7:
                resumen['areas_criticas'].append('Contornos y formas necesitan refinamiento')
            
            # Identificar fortalezas
            if analisis.get('ssim_basico', {}).get('ssim_global', 0.0) > 0.9:
                resumen['fortalezas'].append('Excelente similitud estructural básica')
            
            # Generar recomendaciones
            if fidelidad < 0.98:
                resumen['recomendaciones_mejora'] = [
                    'Ajustar coordenadas principales',
                    'Corregir proporciones geométricas',
                    'Optimizar elementos de contorno'
                ]
            
            return resumen
            
        except Exception as e:
            self.logger.error(f"❌ Error generando resumen: {e}")
            return {'error': str(e)}

def test_analizador_basico():
    """Test del analizador básico sin entornos virtuales."""
    print("🧪 Probando analizador básico sin entornos virtuales...")
    
    try:
        analizador = AnalizadorBasicoSinVenv()
        
        # Crear imágenes de prueba
        img_test = np.random.rand(100, 100).astype(np.float64)
        
        # Test de métodos básicos
        ssim_result = analizador._calcular_ssim_basico(img_test, img_test)
        mse_result = analizador._calcular_mse_psnr(img_test, img_test)
        
        print(f"✅ SSIM básico: {ssim_result.get('ssim_global', 0.0):.4f}")
        print(f"✅ MSE normalizado: {mse_result.get('mse_normalizado', 0.0):.4f}")
        print("🎯 Analizador básico funcionando correctamente")
        
    except Exception as e:
        print(f"❌ Error en test: {e}")

if __name__ == "__main__":
    test_analizador_basico()
