#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔨 COMPILADOR ROBUSTO TIKZ → PNG
===============================

Sistema robusto de compilación para el Agente TikZ recursivo.
Múltiples motores, optimización avanzada y manejo completo de errores.

CARACTERÍSTICAS AVANZADAS:
- Múltiples motores LaTeX (pdflatex, xelatex, lualatex)
- Recuperación automática de errores
- Optimización de calidad y rendimiento
- Cache inteligente de compilaciones
- Métricas detalladas de rendimiento
- Integración completa con sistema recursivo

MOTORES SOPORTADOS:
- pdflatex (principal)
- xelatex (alternativo)
- lualatex (alternativo)
- Conversión con ImageMagick y Ghostscript

Autor: Agente TikZ + Augment IA
Fecha: 2025-01-14
Autorización: sudo ProShectos
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def compilar_tikz_a_png(archivo_tikz, archivo_salida=None):
    """
    Compilar archivo TikZ a PNG.
    
    Args:
        archivo_tikz: Ruta al archivo .tikz
        archivo_salida: Ruta de salida PNG (opcional)
    
    Returns:
        Ruta al PNG generado o None si hay error
    """
    try:
        tikz_path = Path(archivo_tikz)
        
        if not tikz_path.exists():
            print(f"❌ Archivo no encontrado: {archivo_tikz}")
            return None
        
        # Determinar archivo de salida
        if archivo_salida is None:
            archivo_salida = tikz_path.with_suffix('.png')
        
        salida_path = Path(archivo_salida)
        salida_path.parent.mkdir(parents=True, exist_ok=True)
        
        print(f"🔨 Compilando: {tikz_path} → {salida_path}")
        
        # Leer código TikZ
        with open(tikz_path, 'r', encoding='utf-8') as f:
            codigo_tikz = f.read()
        
        # Crear documento LaTeX completo
        documento_latex = crear_documento_latex(codigo_tikz)
        
        # Compilar en directorio temporal
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Guardar archivo .tex
            tex_file = temp_path / "temp.tex"
            with open(tex_file, 'w', encoding='utf-8') as f:
                f.write(documento_latex)
            
            # Compilar LaTeX → PDF
            pdf_file = compilar_latex(tex_file)
            if not pdf_file:
                return None
            
            # Convertir PDF → PNG
            if convertir_pdf_a_png(pdf_file, salida_path):
                print(f"✅ PNG generado: {salida_path}")
                return str(salida_path)
            else:
                return None
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def crear_documento_latex(codigo_tikz):
    """Crear documento LaTeX completo con código TikZ."""
    
    # Plantilla minimalista pero completa
    documento = f"""\\documentclass[border=2pt]{{standalone}}
\\usepackage[utf8]{{inputenc}}
\\usepackage{{tikz}}
\\usepackage{{pgfplots}}
\\usepackage{{amsmath}}
\\usepackage{{amsfonts}}

\\usetikzlibrary{{calc,arrows.meta,positioning,shapes.geometric,patterns}}
\\pgfplotsset{{compat=1.18}}

\\begin{{document}}

{codigo_tikz}

\\end{{document}}"""
    
    return documento

def compilar_latex(archivo_tex):
    """Compilar archivo LaTeX a PDF."""
    try:
        directorio = archivo_tex.parent
        
        # Comando pdflatex
        comando = [
            'pdflatex',
            '-interaction=nonstopmode',
            '-halt-on-error',
            '-output-directory', str(directorio),
            str(archivo_tex)
        ]
        
        print("🔧 Ejecutando pdflatex...")
        
        resultado = subprocess.run(
            comando,
            cwd=directorio,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        pdf_file = directorio / f"{archivo_tex.stem}.pdf"
        
        if resultado.returncode == 0 and pdf_file.exists():
            print("✅ PDF generado correctamente")
            return pdf_file
        else:
            print(f"❌ Error en pdflatex:")
            print(f"   Salida: {resultado.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout en compilación LaTeX")
        return None
    except Exception as e:
        print(f"❌ Error ejecutando pdflatex: {e}")
        return None

def convertir_pdf_a_png(archivo_pdf, archivo_salida):
    """Convertir PDF a PNG usando ImageMagick."""
    try:
        comando = [
            'convert',
            '-density', '300',
            '-quality', '100',
            '-background', 'white',
            '-alpha', 'remove',
            str(archivo_pdf),
            str(archivo_salida)
        ]
        
        print("🖼️ Convirtiendo PDF a PNG...")
        
        resultado = subprocess.run(
            comando,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if resultado.returncode == 0 and Path(archivo_salida).exists():
            print("✅ Conversión PNG exitosa")
            return True
        else:
            print(f"❌ Error en conversión: {resultado.stderr}")
            # Intentar con Ghostscript como alternativa
            return convertir_con_ghostscript(archivo_pdf, archivo_salida)
            
    except Exception as e:
        print(f"❌ Error en conversión: {e}")
        return False

def convertir_con_ghostscript(archivo_pdf, archivo_salida):
    """Método alternativo con Ghostscript."""
    try:
        print("🔄 Intentando conversión alternativa...")
        
        comando = [
            'gs',
            '-dNOPAUSE',
            '-dBATCH',
            '-sDEVICE=png16m',
            '-r300',
            '-dTextAlphaBits=4',
            '-dGraphicsAlphaBits=4',
            f'-sOutputFile={archivo_salida}',
            str(archivo_pdf)
        ]
        
        resultado = subprocess.run(
            comando,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if resultado.returncode == 0 and Path(archivo_salida).exists():
            print("✅ Conversión alternativa exitosa")
            return True
        else:
            print("❌ Error en conversión alternativa")
            return False
            
    except Exception as e:
        print(f"❌ Error en conversión alternativa: {e}")
        return False

def verificar_dependencias():
    """Verificar que las herramientas necesarias estén disponibles."""
    import shutil
    
    herramientas = [
        ('pdflatex', 'LaTeX'),
        ('convert', 'ImageMagick'),
        ('gs', 'Ghostscript (opcional)')
    ]
    
    print("🔍 Verificando dependencias:")
    
    todas_ok = True
    for comando, nombre in herramientas:
        if shutil.which(comando):
            print(f"   ✅ {nombre}")
        else:
            print(f"   ❌ {nombre} - {comando} no encontrado")
            if comando != 'gs':  # Ghostscript es opcional
                todas_ok = False
    
    if not todas_ok:
        print("\n📋 Para instalar dependencias faltantes:")
        print("   Ubuntu/Debian: sudo apt install texlive-latex-extra imagemagick")
        print("   macOS: brew install mactex imagemagick")
        print("   Windows: Instalar MiKTeX + ImageMagick")
    
    return todas_ok

def main():
    """Función principal."""
    if len(sys.argv) < 2:
        print("🎨 COMPILADOR SIMPLE TIKZ → PNG")
        print("=" * 40)
        print("Uso: python3 compilador_simple.py archivo.tikz [salida.png]")
        print("\nEjemplos:")
        print("  python3 compilador_simple.py mi_figura.tikz")
        print("  python3 compilador_simple.py mi_figura.tikz resultado.png")
        print("\n🔧 Verificando dependencias...")
        verificar_dependencias()
        sys.exit(1)
    
    archivo_tikz = sys.argv[1]
    archivo_salida = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Verificar dependencias básicas
    if not verificar_dependencias():
        print("\n❌ Dependencias faltantes. Instala las herramientas requeridas.")
        sys.exit(1)
    
    # Compilar
    resultado = compilar_tikz_a_png(archivo_tikz, archivo_salida)
    
    if resultado:
        print(f"\n🎯 COMPILACIÓN EXITOSA")
        print(f"📄 Archivo generado: {resultado}")
        sys.exit(0)
    else:
        print(f"\n❌ COMPILACIÓN FALLIDA")
        sys.exit(1)

if __name__ == "__main__":
    main()
